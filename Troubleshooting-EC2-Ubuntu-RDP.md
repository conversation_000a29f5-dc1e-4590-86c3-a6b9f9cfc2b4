# Guide: Configuring RDP and SSH on Ubuntu 24.04 EC2

This guide provides a comprehensive walkthrough for setting up an Ubuntu 24.04 EC2 instance with a graphical desktop environment (XFCE) accessible via RDP and standard SSH.

## 1. Prerequisites

### Local Machine (Windows)
- An SSH client (e.g., OpenSSH, PuTTY).
- Remote Desktop Connection client (`mstsc.exe`).
- Your AWS EC2 private key file (`.pem`).

### AWS
- An AWS account with permissions to create EC2 instances and manage security groups.
- An existing EC2 key pair or the ability to create a new one.

## 2. EC2 Instance Setup

1.  **Launch a new EC2 Instance** with the following configuration:
    *   **AMI:** Ubuntu Server 24.04 LTS
    *   **Instance Type:** `t3.medium` or higher is recommended for a GUI.
    *   **Key Pair:** Select your existing `.pem` key.
    *   **Security Group (Inbound Rules):**
        *   **SSH** (Port `22`) from your IP address.
        *   **RDP** (Port `3389`) from your IP address.

2.  **Allocate and Associate an Elastic IP:** To have a static public IP for your instance, allocate an Elastic IP and associate it with your newly created EC2 instance.

## 3. SSH Access

Connect to your instance using its public IP and your private key to perform the setup.

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

## 4. Desktop and RDP Server Installation

### Step 4.1: Update System and Install XFCE Desktop

Once connected via SSH, update your package manager and install the XFCE desktop environment.

```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y xfce4 xfce4-goodies
```

### Step 4.2: Install and Configure XRDP

Install the `xrdp` server, which provides the RDP functionality.

```bash
sudo apt install -y xrdp
sudo systemctl enable xrdp
sudo systemctl start xrdp
```

### Step 4.3: Set a Password for the `ubuntu` User

RDP requires a password for authentication. Set one for the default `ubuntu` user.

```bash
sudo passwd ubuntu
```
You will be prompted to enter and confirm a new password.

## 5. Final Configuration for RDP

This section contains the critical fixes derived from the troubleshooting session.

### Step 5.1: Configure the Session Manager

The `xrdp` session manager needs to know which desktop environment to start.

Create a `.xsession` file in the `ubuntu` user's home directory to explicitly start XFCE.

```bash
echo "xfce4-session" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession
```

### Step 5.2: Correct the `startwm.sh` Script

The `startwm.sh` script is the main entry point for an `xrdp` session. The default script can cause issues. Replace it with a more robust version that correctly handles session startup.

First, back up the original file:
```bash
sudo cp /etc/xrdp/startwm.sh /etc/xrdp/startwm.sh.backup
```

Now, replace its content:
```bash
sudo tee /etc/xrdp/startwm.sh > /dev/null << 'EOF'
#!/bin/sh
# xrdp X session start script

# Load profile scripts if they exist
if [ -r /etc/profile ]; then
    . /etc/profile
fi
if [ -r "$HOME/.profile" ]; then
    . "$HOME/.profile"
fi

# Unset variables that can interfere with the session
unset DBUS_SESSION_BUS_ADDRESS
unset XDG_RUNTIME_DIR

# Start the window manager
/etc/X11/Xsession
EOF
```
Make the new script executable:
```bash
sudo chmod +x /etc/xrdp/startwm.sh
```

### Step 5.3: Address `polkit` Issues

Authentication pop-ups for actions like color management or software updates can cause a black screen or session termination on RDP. Create a policy file to grant permissions and prevent this.

```bash
sudo bash -c 'cat > /etc/polkit-1/localauthority/50-local.d/45-allow-colord.pkla <<EOF
[Allow Colord all Users]
Identity=unix-user:*
Action=org.freedesktop.color-manager.create-device;org.freedesktop.color-manager.create-profile;org.freedesktop.color-manager.delete-device;org.freedesktop.color-manager.delete-profile;org.freedesktop.color-manager.modify-device;org.freedesktop.color-manager.modify-profile
ResultAny=no
ResultInactive=no
ResultActive=yes
EOF'
```

### Step 5.4: Restart Services

Apply all the changes by restarting the `xrdp` service.

```bash
sudo systemctl restart xrdp
```

## 6. Connecting via RDP

You should now be able to connect.

1.  Open the **Remote Desktop Connection** client on Windows (`mstsc`).
2.  Enter the **Public IP** of your EC2 instance.
3.  When prompted, use the following credentials:
    *   **Username:** `ubuntu`
    *   **Password:** The password you set in Step 4.3.

You should see the XFCE desktop environment.

## 7. Troubleshooting

If the RDP window closes immediately after login, the issue is almost always with the session manager failing to start the desktop.

**Key Log Files:**
- `/var/log/xrdp.log`
- `/var/log/xrdp-sesman.log`

**Common Error and Solution:**

- **Error in `xrdp-sesman.log`:**
  ```
  Window manager (pid XXXX, display 10) exited with non-zero exit code 127
  ```
  This means "command not found." The `startwm.sh` script cannot find the desktop session command.

- **Solution:**
  1.  Ensure XFCE is fully installed: `sudo apt install xfce4 xfce4-goodies`
  2.  Verify the `~/.xsession` file exists and contains `xfce4-session`.
  3.  Ensure the `/etc/xrdp/startwm.sh` script is correct, as detailed in Step 5.2.
  4.  Restart xrdp: `sudo systemctl restart xrdp`

## 8. Resetting RDP Password for Ubuntu User

If you've forgotten the RDP password that was set for the `ubuntu` user during initial configuration, you can reset it using SSH access. This section provides multiple methods to reset the password depending on your access situation.

### Method 1: Reset Password via SSH (Recommended)

This is the most straightforward method if you still have SSH access to your EC2 instance.

#### Step 8.1: Connect via SSH

Connect to your EC2 instance using your private key:

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

#### Step 8.2: Reset the Ubuntu User Password

Once connected via SSH, reset the password for the `ubuntu` user:

```bash
sudo passwd ubuntu
```

You will be prompted to:
1. Enter a new password
2. Confirm the new password

**Example output:**
```
New password: [enter your new password]
Retype new password: [confirm your new password]
passwd: password updated successfully
```

#### Step 8.3: Verify RDP Service Status

Ensure the RDP service is running properly:

```bash
sudo systemctl status xrdp
```

If it's not running, start it:

```bash
sudo systemctl start xrdp
sudo systemctl enable xrdp
```

#### Step 8.4: Test RDP Connection

1. Open **Remote Desktop Connection** on Windows (`mstsc`)
2. Enter your EC2 instance's public IP
3. Use the new credentials:
   - **Username:** `ubuntu`
   - **Password:** Your newly set password

### Method 2: Reset Password via AWS Systems Manager (Alternative)

If you have AWS Systems Manager Session Manager enabled, you can use it as an alternative to SSH.

#### Step 8.1: Enable Session Manager (if not already enabled)

1. In AWS Console, go to **EC2** → **Instances**
2. Select your instance
3. Ensure it has an IAM role with `AmazonSSMManagedInstanceCore` policy
4. If not, create and attach the role:

```bash
# Create IAM role (via AWS CLI)
aws iam create-role --role-name EC2-SSM-Role --assume-role-policy-document '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}'

# Attach policy
aws iam attach-role-policy --role-name EC2-SSM-Role --policy-arn arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore

# Create instance profile
aws iam create-instance-profile --instance-profile-name EC2-SSM-Profile
aws iam add-role-to-instance-profile --instance-profile-name EC2-SSM-Profile --role-name EC2-SSM-Role
```

#### Step 8.2: Connect via Session Manager

1. In AWS Console, go to **Systems Manager** → **Session Manager**
2. Click **Start session**
3. Select your EC2 instance
4. Click **Start session**

#### Step 8.3: Reset Password

In the Session Manager terminal:

```bash
sudo passwd ubuntu
```

### Method 3: Reset Password via EC2 User Data (Advanced)

If you don't have SSH access and Session Manager isn't available, you can use EC2 User Data to reset the password on next boot.

#### Step 8.1: Stop the Instance

1. In AWS Console, go to **EC2** → **Instances**
2. Select your instance
3. Click **Instance state** → **Stop instance**

#### Step 8.2: Modify User Data

1. Right-click the stopped instance → **Instance settings** → **Edit user data**
2. Add the following script:

```bash
#!/bin/bash
# Reset ubuntu user password
echo 'ubuntu:YourNewPassword123!' | chpasswd
# Remove this user data after execution (optional)
# echo "" > /var/lib/cloud/instance/user-data.txt
```

**⚠️ Security Note:** Replace `YourNewPassword123!` with your desired password. Be aware that user data is visible in the EC2 console.

#### Step 8.3: Start the Instance

1. Click **Instance state** → **Start instance**
2. Wait for the instance to boot completely
3. The password will be reset during the boot process

#### Step 8.4: Clear User Data (Security Best Practice)

After the password is reset:
1. Stop the instance again
2. Edit user data and clear the content
3. Start the instance

### Method 4: Create New User with RDP Access (Alternative Solution)

If you prefer to create a new user instead of resetting the existing password:

#### Step 4.1: Connect via SSH

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

#### Step 4.2: Create New User

```bash
# Create new user
sudo adduser newrdpuser

# Add user to sudo group (optional)
sudo usermod -aG sudo newrdpuser

# Set up RDP session for new user
echo "xfce4-session" | sudo tee /home/<USER>/.xsession
sudo chown newrdpuser:newrdpuser /home/<USER>/.xsession
```

#### Step 4.3: Test New User RDP Access

Use the new username and password for RDP connection.

### Password Security Best Practices

#### Strong Password Requirements
- **Minimum 12 characters**
- **Mix of uppercase, lowercase, numbers, and symbols**
- **Avoid common words or patterns**
- **Example format:** `MySecure2024!RDP`

#### Additional Security Measures

1. **Enable SSH Key Authentication Only** (disable password auth for SSH):
   ```bash
   sudo nano /etc/ssh/sshd_config
   # Set: PasswordAuthentication no
   sudo systemctl restart ssh
   ```

2. **Restrict RDP Access by IP**:
   - In AWS Security Group, limit RDP (port 3389) to specific IP addresses
   - Avoid using `0.0.0.0/0` (anywhere)

3. **Enable AWS CloudTrail** for audit logging:
   ```bash
   # Monitor RDP login attempts in CloudWatch logs
   sudo tail -f /var/log/xrdp.log
   ```

4. **Regular Password Rotation**:
   - Change RDP password every 90 days
   - Document password changes in your security log

### Troubleshooting Password Reset Issues

#### Issue 1: "Authentication failure" after password reset

**Solution:**
```bash
# Restart xrdp service
sudo systemctl restart xrdp

# Check service status
sudo systemctl status xrdp

# Verify user exists and has password set
sudo getent passwd ubuntu
```

#### Issue 2: User data script didn't execute

**Solution:**
```bash
# Check cloud-init logs
sudo cat /var/log/cloud-init-output.log
sudo cat /var/log/cloud-init.log

# Manually run user data script
sudo bash /var/lib/cloud/instance/user-data.txt
```

#### Issue 3: Session Manager not working

**Solution:**
```bash
# Install SSM agent (if missing)
sudo snap install amazon-ssm-agent --classic
sudo systemctl start snap.amazon-ssm-agent.amazon-ssm-agent.service
sudo systemctl enable snap.amazon-ssm-agent.amazon-ssm-agent.service
```

### Verification Checklist

After resetting the password, verify the following:

- [ ] RDP service is running: `sudo systemctl status xrdp`
- [ ] User password is set: `sudo passwd -S ubuntu`
- [ ] Security group allows RDP (port 3389) from your IP
- [ ] EC2 instance is running and accessible
- [ ] XFCE desktop environment is installed
- [ ] `.xsession` file exists in user's home directory
- [ ] Can connect via RDP with new credentials

## 9. RDP Performance Optimization

If you're experiencing slow RDP performance, especially with cross-region connections (like US-East-1 from India), this section provides comprehensive optimization strategies.

### 9.1 Performance Issues Analysis

#### Common Symptoms:
- **Slow screen updates** and laggy mouse/keyboard response
- **High latency** during remote sessions
- **Choppy video/animation** rendering
- **Frequent disconnections** or timeouts
- **Poor audio quality** (if enabled)

#### Root Causes:
1. **Geographic Distance**: US-East-1 to India (~200-300ms latency)
2. **Heavy Desktop Environment**: XFCE with full features enabled
3. **Unoptimized RDP Settings**: Default XRDP configuration
4. **Network Bandwidth**: Limited or variable internet connection
5. **EC2 Instance Resources**: CPU/Memory constraints during GUI operations

### 9.2 Hardware and Network Optimization

#### Step 9.2.1: Upgrade EC2 Instance Type

Your current `t3a.large` may be insufficient for smooth RDP performance. Consider upgrading:

**Recommended Instance Types:**
```bash
# Current: t3a.large (2 vCPU, 8 GB RAM)
# Better options:
# t3a.xlarge   (4 vCPU, 16 GB RAM) - Good for general use
# c5.xlarge    (4 vCPU, 8 GB RAM)  - CPU optimized
# m5.xlarge    (4 vCPU, 16 GB RAM) - Balanced performance
# c5.2xlarge   (8 vCPU, 16 GB RAM) - High performance
```

**To change instance type:**
1. Stop the EC2 instance
2. Right-click → **Instance Settings** → **Change Instance Type**
3. Select `c5.xlarge` or `m5.xlarge` for better performance
4. Start the instance

#### Step 9.2.2: Regional Optimization

**Option A: Move to Closer Region (Recommended)**
```bash
# Consider these regions for better latency from India:
# ap-south-1     (Mumbai)     - ~10-50ms latency
# ap-southeast-1 (Singapore)  - ~50-100ms latency
# ap-southeast-2 (Sydney)     - ~100-150ms latency
```

**Option B: Use AWS Global Accelerator**
- Improves performance by routing through AWS backbone
- Can reduce latency by 20-60%
- Additional cost but significant performance improvement

#### Step 9.2.3: Network Optimization

**Enable Enhanced Networking:**
```bash
# Check if enhanced networking is enabled
aws ec2 describe-instances --instance-ids i-1234567890abcdef0 --query 'Reservations[].Instances[].EnaSupport'

# Enable enhanced networking (requires instance stop)
aws ec2 modify-instance-attribute --instance-id i-1234567890abcdef0 --ena-support
```

### 9.3 XRDP Configuration Optimization

#### Step 9.3.1: Optimize XRDP Settings

Create optimized XRDP configuration:

```bash
# Backup original configuration
sudo cp /etc/xrdp/xrdp.ini /etc/xrdp/xrdp.ini.backup

# Apply performance optimizations
sudo tee /etc/xrdp/xrdp.ini > /dev/null << 'EOF'
[Globals]
ini_version=1
fork=true
port=3389
tcp_nodelay=true
tcp_keepalive=true
security_layer=rdp
crypt_level=high
certificate=
key_file=
ssl_protocols=TLSv1.2, TLSv1.3
autorun=
allow_channels=true
allow_multimon=true
bitmap_cache=true
bitmap_compression=true
bulk_compression=true
max_bpp=24
new_cursors=true
use_fastpath=both
require_credentials=true
login_retry=4
killloggedon=false
rdp_keepalive=true

[Xorg]
name=Xorg
lib=libxup.so
username=ask
password=ask
ip=127.0.0.1
port=-1
code=20
EOF
```

#### Step 9.3.2: Optimize Session Manager

```bash
# Edit session manager configuration
sudo nano /etc/xrdp/sesman.ini

# Add these optimizations to [Xorg] section:
# param1=-bs
# param2=-ac
# param3=-nolisten
# param4=tcp
# param5=-dpi
# param6=96
```

Or apply automatically:
```bash
sudo tee -a /etc/xrdp/sesman.ini > /dev/null << 'EOF'

[Xorg]
param1=-bs
param2=-ac
param3=-nolisten
param4=tcp
param5=-dpi
param6=96
EOF
```

### 9.4 Desktop Environment Optimization

#### Step 9.4.1: Switch to Lightweight Desktop (Recommended)

Replace XFCE with an even lighter desktop environment:

**Option A: LXDE (Lightest)**
```bash
# Remove XFCE (optional)
sudo apt remove --purge xfce4* -y
sudo apt autoremove -y

# Install LXDE
sudo apt update
sudo apt install -y lxde-core lxde-common

# Configure for RDP
echo "startlxde" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession
```

**Option B: LXQt (Modern and Light)**
```bash
# Install LXQt
sudo apt install -y lxqt-core

# Configure for RDP
echo "startlxqt" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession
```

**Option C: Optimize Existing XFCE**
```bash
# Disable unnecessary XFCE components
xfconf-query -c xfwm4 -p /general/use_compositing -s false
xfconf-query -c xfwm4 -p /general/frame_opacity -s 100
xfconf-query -c xfwm4 -p /general/inactive_opacity -s 100

# Disable desktop effects
xfconf-query -c xfwm4 -p /general/show_frame_shadow -s false
xfconf-query -c xfwm4 -p /general/show_popup_shadow -s false

# Set simple theme
xfconf-query -c xsettings -p /Net/ThemeName -s "Adwaita"
xfconf-query -c xfwm4 -p /general/theme -s "Default"
```

#### Step 9.4.2: Disable Resource-Heavy Services

```bash
# Disable unnecessary services
sudo systemctl disable bluetooth
sudo systemctl disable cups
sudo systemctl disable avahi-daemon
sudo systemctl disable ModemManager

# Stop services immediately
sudo systemctl stop bluetooth
sudo systemctl stop cups
sudo systemctl stop avahi-daemon
sudo systemctl stop ModemManager

# Remove unnecessary packages
sudo apt remove --purge thunderbird libreoffice-* -y
sudo apt autoremove -y
```

#### Step 9.4.3: Optimize Display Settings

```bash
# Create display optimization script
cat > /home/<USER>/optimize-display.sh << 'EOF'
#!/bin/bash
# Reduce color depth for better performance
xrandr --output default --depth 16

# Disable screen saver and power management
xset s off
xset -dpms
xset s noblank

# Optimize font rendering
echo "Xft.antialias: 0" >> ~/.Xresources
echo "Xft.hinting: 0" >> ~/.Xresources
xrdb ~/.Xresources
EOF

chmod +x /home/<USER>/optimize-display.sh

# Auto-run on session start
echo "/home/<USER>/optimize-display.sh" >> /home/<USER>/.xsession
```

### 9.5 Client-Side RDP Optimization

#### Step 9.5.1: Windows RDP Client Settings

**Optimize Remote Desktop Connection:**

1. **Open Remote Desktop Connection** (`mstsc`)
2. **Click "Show Options"**
3. **Display Tab:**
   - Set **Color depth** to **High Color (16 bit)**
   - **Uncheck** "Display the connection bar when I use the full screen"

4. **Local Resources Tab:**
   - **Uncheck** "Printers" and "Clipboard" if not needed
   - **Audio:** Set to "Do not play"

5. **Experience Tab:**
   - **Connection speed:** Select "LAN (10 Mbps or higher)"
   - **Uncheck all visual effects:**
     - Desktop background
     - Font smoothing
     - Desktop composition
     - Show contents of windows while dragging
     - Menu and window animation
     - Themes

6. **Advanced Tab:**
   - **Server authentication:** "Connect and don't warn me"

#### Step 9.5.2: Alternative RDP Clients

**Consider using optimized RDP clients:**

**FreeRDP (Command Line):**
```bash
# Install on local Windows (via WSL) or Linux
sudo apt install freerdp2-x11

# Connect with optimized settings
xfreerdp /v:your-ec2-ip /u:ubuntu /p:yourpassword /bpp:16 /compression /fast-path /gfx-progressive /rfx
```

**RDP Wrapper (Windows):**
- Download and install RDP Wrapper
- Provides better compression and performance options

### 9.6 System Performance Monitoring

#### Step 9.6.1: Monitor Resource Usage

```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Create monitoring script
cat > /home/<USER>/monitor-performance.sh << 'EOF'
#!/bin/bash
echo "=== CPU Usage ==="
top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}'

echo "=== Memory Usage ==="
free -h | awk 'NR==2{printf "Memory Usage: %s/%s (%.2f%%)\n", $3,$2,$3*100/$2 }'

echo "=== Disk Usage ==="
df -h | awk '$NF=="/"{printf "Disk Usage: %d/%dGB (%s)\n", $3,$2,$5}'

echo "=== Network Connections ==="
ss -tuln | grep :3389
EOF

chmod +x /home/<USER>/monitor-performance.sh
```

#### Step 9.6.2: Performance Testing

```bash
# Test network latency to your location
ping -c 10 *******

# Test bandwidth
sudo apt install speedtest-cli
speedtest-cli

# Monitor RDP connections
sudo tail -f /var/log/xrdp.log
```

### 9.7 Advanced Optimization Techniques

#### Step 9.7.1: Kernel Parameter Tuning

```bash
# Optimize network parameters
sudo tee -a /etc/sysctl.conf > /dev/null << 'EOF'
# Network optimizations for RDP
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
EOF

# Apply changes
sudo sysctl -p
```

#### Step 9.7.2: GPU Acceleration (if available)

```bash
# Check for GPU
lspci | grep -i vga

# Install GPU drivers if available
sudo apt install -y mesa-utils

# Enable GPU acceleration for X11
sudo nano /etc/X11/xorg.conf.d/20-intel.conf
# Add:
# Section "Device"
#    Identifier "Intel Graphics"
#    Driver "intel"
#    Option "AccelMethod" "sna"
#    Option "TearFree" "true"
# EndSection
```

### 9.8 Performance Benchmarking

#### Step 9.8.1: Before and After Testing

**Test RDP Performance:**
```bash
# Create performance test script
cat > /home/<USER>/rdp-performance-test.sh << 'EOF'
#!/bin/bash
echo "Starting RDP Performance Test..."

# Test 1: Window movement
echo "Test 1: Moving windows (should be smooth)"
for i in {1..10}; do
    xterm -geometry 80x24+$((i*50))+$((i*30)) -e "sleep 2" &
done
sleep 5
killall xterm

# Test 2: Text rendering
echo "Test 2: Text rendering speed"
time (for i in {1..100}; do echo "Performance test line $i"; done)

# Test 3: Graphics performance
echo "Test 3: Basic graphics test"
glxgears -info | head -5

echo "Performance test completed"
EOF

chmod +x /home/<USER>/rdp-performance-test.sh
```

### 9.9 Troubleshooting Performance Issues

#### Common Issues and Solutions:

**Issue 1: High CPU Usage**
```bash
# Check processes
htop
# Kill unnecessary processes
sudo pkill -f firefox
sudo pkill -f chrome
```

**Issue 2: Memory Exhaustion**
```bash
# Check memory usage
free -h
# Clear cache
sudo sync && echo 3 | sudo tee /proc/sys/vm/drop_caches
```

**Issue 3: Network Latency**
```bash
# Test latency to your location
mtr -r -c 10 your-local-ip
# Consider changing AWS region
```

### 9.10 Restart Services and Apply Changes

After making all optimizations:

```bash
# Restart all relevant services
sudo systemctl restart xrdp
sudo systemctl restart lightdm  # or gdm3

# Reboot for kernel parameter changes
sudo reboot
```

### 9.11 Performance Optimization Checklist

After implementing optimizations, verify:

- [ ] EC2 instance type upgraded (c5.xlarge or better)
- [ ] Enhanced networking enabled
- [ ] XRDP configuration optimized
- [ ] Lightweight desktop environment installed
- [ ] Unnecessary services disabled
- [ ] Client RDP settings optimized
- [ ] Network parameters tuned
- [ ] Performance monitoring tools installed
- [ ] Latency tested and acceptable (<150ms)

**Expected Performance Improvements:**
- **50-70% reduction** in screen update lag
- **30-50% improvement** in mouse/keyboard responsiveness
- **Reduced CPU usage** from 60-80% to 20-40%
- **Better stability** with fewer disconnections

## 10. Complete Migration to Mumbai Region with Optimized Setup

If you're experiencing terminal emulator issues and Ubuntu errors, the best solution is a fresh setup in Mumbai region with optimized configuration for your specific needs (VS Code + CLI terminals).

### 10.1 Current Environment Diagnosis

#### Step 10.1.1: Check Current Desktop Environment

First, let's identify what's currently installed:

```bash
# Check current desktop environment
echo $XDG_CURRENT_DESKTOP
echo $DESKTOP_SESSION

# Check installed desktop packages
dpkg -l | grep -E "(xfce|lxde|gnome|kde|mate)"

# Check running desktop processes
ps aux | grep -E "(xfce|lxde|gnome|kde)"

# Check display manager
systemctl status lightdm
systemctl status gdm3
systemctl status sddm
```

#### Step 10.1.2: Check Current Issues

```bash
# Check system errors
sudo journalctl -p 3 -xb

# Check RDP logs for errors
sudo tail -50 /var/log/xrdp.log
sudo tail -50 /var/log/xrdp-sesman.log

# Check if terminal emulator is installed
which gnome-terminal
which xfce4-terminal
which lxterminal
which xterm
```

### 10.2 Mumbai Region Migration Strategy

#### Step 10.2.1: Create AMI from Current Instance (Backup)

Before migration, create a backup:

```bash
# From AWS CLI or Console:
# 1. Stop current instance
# 2. Create AMI: "ubuntu-rdp-backup-YYYY-MM-DD"
# 3. Note AMI ID for potential rollback
```

#### Step 10.2.2: Launch New Instance in ap-south-1 (Mumbai)

**Recommended Configuration:**
- **Region**: ap-south-1 (Asia Pacific - Mumbai)
- **AMI**: Ubuntu Server 24.04 LTS
- **Instance Type**: `c5.xlarge` (4 vCPU, 8 GB RAM) - Optimal for VS Code + terminals
- **Storage**: 30 GB GP3 SSD (better performance than GP2)
- **Security Group**:
  - SSH (22) from your IP
  - RDP (3389) from your IP

**Launch Command (AWS CLI):**
```bash
aws ec2 run-instances \
    --region ap-south-1 \
    --image-id ami-0f58b397bc5c1f2e8 \
    --instance-type c5.xlarge \
    --key-name your-key-pair \
    --security-group-ids sg-xxxxxxxxx \
    --block-device-mappings '[{"DeviceName":"/dev/sda1","Ebs":{"VolumeSize":30,"VolumeType":"gp3"}}]' \
    --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=Ubuntu-RDP-Mumbai}]'
```

### 10.3 Optimized LXDE Setup for VS Code Development

#### Step 10.3.1: Connect and Update System

```bash
# Connect to new Mumbai instance
ssh -i "your-key.pem" ubuntu@new-mumbai-ip

# Update system
sudo apt update && sudo apt upgrade -y
```

#### Step 10.3.2: Install Minimal LXDE Desktop

```bash
# Install only essential LXDE components
sudo apt install -y \
    lxde-core \
    lxde-common \
    lxterminal \
    pcmanfm \
    lxpanel \
    openbox \
    obconf \
    lxappearance

# Install essential utilities
sudo apt install -y \
    firefox \
    gedit \
    file-manager \
    gvfs \
    gvfs-backends
```

#### Step 10.3.3: Install and Configure XRDP

```bash
# Install XRDP
sudo apt install -y xrdp

# Create optimized XRDP configuration for LXDE
sudo tee /etc/xrdp/xrdp.ini > /dev/null << 'EOF'
[Globals]
ini_version=1
fork=true
port=3389
tcp_nodelay=true
tcp_keepalive=true
security_layer=rdp
crypt_level=high
bitmap_cache=true
bitmap_compression=true
bulk_compression=true
max_bpp=24
new_cursors=true
use_fastpath=both
require_credentials=true
rdp_keepalive=true

[Xorg]
name=Xorg
lib=libxup.so
username=ask
password=ask
ip=127.0.0.1
port=-1
code=20
EOF

# Configure session for LXDE
echo "startlxde" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession

# Set password for ubuntu user
sudo passwd ubuntu

# Enable and start XRDP
sudo systemctl enable xrdp
sudo systemctl start xrdp
```

#### Step 10.3.4: Optimize LXDE for Development

```bash
# Create LXDE optimization script
cat > /home/<USER>/optimize-lxde.sh << 'EOF'
#!/bin/bash

# Disable unnecessary LXDE components
mkdir -p ~/.config/autostart

# Disable screensaver
echo "[Desktop Entry]
Type=Application
Name=Disable Screensaver
Exec=xset s off -dpms
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true" > ~/.config/autostart/disable-screensaver.desktop

# Configure LXTerminal for development
mkdir -p ~/.config/lxterminal
cat > ~/.config/lxterminal/lxterminal.conf << 'TERM_EOF'
[general]
fontname=Monospace 11
selchars=-A-Za-z0-9,./?%&#:_
scrollback=1000
bgcolor=rgb(0,0,0)
fgcolor=rgb(255,255,255)
palette_color_0=rgb(0,0,0)
palette_color_1=rgb(178,24,24)
palette_color_2=rgb(24,178,24)
palette_color_3=rgb(178,104,24)
palette_color_4=rgb(24,24,178)
palette_color_5=rgb(178,24,178)
palette_color_6=rgb(24,178,178)
palette_color_7=rgb(178,178,178)
palette_color_8=rgb(104,104,104)
palette_color_9=rgb(255,84,84)
palette_color_10=rgb(84,255,84)
palette_color_11=rgb(255,255,84)
palette_color_12=rgb(84,84,255)
palette_color_13=rgb(255,84,255)
palette_color_14=rgb(84,255,255)
palette_color_15=rgb(255,255,255)
color_preset=Custom
disallowbold=false
cursorblinks=false
cursorunderline=false
audiblebell=false
tabpos=top
geometry_columns=80
geometry_rows=24
hidescrollbar=false
hidemenubar=false
hideclosebutton=false
hidepointer=false
disallowbold=false
TERM_EOF

# Configure PCManFM (file manager)
mkdir -p ~/.config/pcmanfm/LXDE
cat > ~/.config/pcmanfm/LXDE/pcmanfm.conf << 'FM_EOF'
[config]
bm_open_method=0
su_cmd=gksu %s
terminal=lxterminal
archiver=file-roller

[volume]
mount_on_startup=1
mount_removable=1
autorun=1

[ui]
always_show_tabs=0
max_tab_chars=32
win_width=640
win_height=480
splitter_pos=150
media_in_new_tab=0
desktop_folder_new_win=0
change_tab_on_drop=1
close_on_unmount=1
focus_previous=0
side_pane_mode=places
view_mode=icon_view
show_hidden=0
sort=name;ascending;
toolbar=newtab;navigation;home;
show_statusbar=1
pathbar_mode_buttons=0
FM_EOF

echo "LXDE optimization completed!"
EOF

chmod +x /home/<USER>/optimize-lxde.sh
./optimize-lxde.sh
```

### 10.4 Install VS Code for Development

#### Step 10.4.1: Install VS Code

```bash
# Install VS Code
sudo apt update
sudo apt install -y wget gpg software-properties-common

# Add Microsoft GPG key and repository
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'

# Install VS Code
sudo apt update
sudo apt install -y code

# Install essential development tools
sudo apt install -y \
    git \
    curl \
    wget \
    build-essential \
    python3 \
    python3-pip \
    nodejs \
    npm \
    default-jdk
```

#### Step 10.4.2: Configure VS Code for RDP

```bash
# Create VS Code settings for optimal RDP performance
mkdir -p ~/.config/Code/User

cat > ~/.config/Code/User/settings.json << 'EOF'
{
    "window.titleBarStyle": "custom",
    "editor.fontFamily": "monospace",
    "editor.fontSize": 14,
    "editor.renderWhitespace": "none",
    "editor.minimap.enabled": false,
    "workbench.activityBar.visible": true,
    "workbench.statusBar.visible": true,
    "workbench.sideBar.location": "left",
    "workbench.editor.enablePreview": false,
    "workbench.startupEditor": "newUntitledFile",
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "terminal.integrated.fontFamily": "monospace",
    "terminal.integrated.fontSize": 12,
    "workbench.colorTheme": "Default Dark+",
    "editor.smoothScrolling": false,
    "workbench.list.smoothScrolling": false,
    "editor.cursorBlinking": "solid",
    "editor.cursorSmoothCaretAnimation": false,
    "workbench.reduceMotion": "on",
    "window.dialogStyle": "custom",
    "workbench.tree.renderIndentGuides": "none"
}
EOF
```

### 10.5 Create Development Environment Setup

#### Step 10.5.1: Create Desktop Shortcuts

```bash
# Create desktop directory
mkdir -p ~/Desktop

# Create VS Code shortcut
cat > ~/Desktop/VSCode.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Visual Studio Code
Comment=Code Editing. Redefined.
Exec=/usr/bin/code
Icon=code
Terminal=false
Categories=Development;IDE;
MimeType=text/plain;inode/directory;
EOF

# Create Terminal shortcut
cat > ~/Desktop/Terminal.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Terminal
Comment=Terminal Emulator
Exec=lxterminal
Icon=terminal
Terminal=false
Categories=System;TerminalEmulator;
EOF

# Make shortcuts executable
chmod +x ~/Desktop/*.desktop
```

#### Step 10.5.2: Create Quick Launch Panel

```bash
# Configure LXDE panel for quick access
mkdir -p ~/.config/lxpanel/LXDE/panels

cat > ~/.config/lxpanel/LXDE/panels/panel << 'EOF'
# lxpanel <profile> config file. Manually editing is not recommended.
# Use preference dialog in lxpanel to adjust config when you can.

Global {
    edge=bottom
    allign=left
    margin=0
    widthtype=percent
    width=100
    height=26
    transparent=0
    tintcolor=#000000
    alpha=0
    setdocktype=1
    setpartialstrut=1
    autohide=0
    heightwhenhidden=2
    usefontcolor=1
    fontcolor=#ffffff
    background=1
    backgroundfile=/usr/share/lxpanel/images/background.png
}

Plugin {
    type=menu
    Config {
        image=/usr/share/pixmaps/lubuntu-logo.png
        system {
        }
        separator {
        }
        item {
            command=run
        }
        separator {
        }
        item {
            image=gnome-logout
            command=logout
        }
    }
}

Plugin {
    type=launchbar
    Config {
        Button {
            id=lxterminal.desktop
        }
        Button {
            id=code.desktop
        }
        Button {
            id=pcmanfm.desktop
        }
        Button {
            id=firefox.desktop
        }
    }
}

Plugin {
    type=taskbar
    expand=1
    Config {
        tooltips=1
        IconsOnly=0
        ShowAllDesks=0
        UseMouseWheel=1
        UseUrgencyHint=1
        FlatButton=0
        MaxTaskWidth=150
        spacing=1
    }
}

Plugin {
    type=tray
}

Plugin {
    type=dclock
    Config {
        ClockFmt=%R
        TooltipFmt=%A %x
        BoldFont=0
        IconOnly=0
        CenterText=0
    }
}
EOF
```

### 10.6 Performance Testing and Validation

#### Step 10.6.1: Test RDP Connection

```bash
# Test from your local machine
# 1. Open Remote Desktop Connection
# 2. Connect to Mumbai instance IP
# 3. Username: ubuntu
# 4. Password: [your set password]

# Expected latency from India to Mumbai: 10-50ms
ping -c 10 your-mumbai-instance-ip
```

#### Step 10.6.2: Validate Environment

```bash
# Create validation script
cat > /home/<USER>/validate-setup.sh << 'EOF'
#!/bin/bash

echo "=== Environment Validation ==="

echo "1. Desktop Environment:"
echo "   Current: $XDG_CURRENT_DESKTOP"
echo "   Session: $DESKTOP_SESSION"

echo "2. Available Applications:"
echo "   VS Code: $(which code)"
echo "   Terminal: $(which lxterminal)"
echo "   File Manager: $(which pcmanfm)"

echo "3. System Resources:"
echo "   CPU: $(nproc) cores"
echo "   Memory: $(free -h | awk 'NR==2{print $2}')"
echo "   Disk: $(df -h / | awk 'NR==2{print $4}') available"

echo "4. Network Performance:"
echo "   Instance Region: $(curl -s http://***************/latest/meta-data/placement/region)"
echo "   Instance Type: $(curl -s http://***************/latest/meta-data/instance-type)"

echo "5. RDP Service Status:"
systemctl is-active xrdp

echo "=== Validation Complete ==="
EOF

chmod +x /home/<USER>/validate-setup.sh
./validate-setup.sh
```

### 10.7 Troubleshooting Common Issues

#### Issue 1: Terminal Not Opening in RDP

```bash
# Install alternative terminals
sudo apt install -y xterm gnome-terminal

# Test terminals
xterm &
lxterminal &
gnome-terminal &

# Set default terminal
sudo update-alternatives --install /usr/bin/x-terminal-emulator x-terminal-emulator /usr/bin/lxterminal 50
```

#### Issue 2: VS Code Not Starting

```bash
# Check VS Code installation
code --version

# Start with verbose logging
code --verbose

# Check for missing dependencies
sudo apt install -y libnss3 libatk-bridge2.0-0 libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
```

#### Issue 3: Desktop Environment Errors

```bash
# Reset LXDE configuration
rm -rf ~/.config/lxsession
rm -rf ~/.config/openbox
rm -rf ~/.config/lxpanel

# Restart session
sudo systemctl restart xrdp
```

### 10.8 Migration Checklist

**Pre-Migration:**
- [ ] Create AMI backup of current instance
- [ ] Document current applications and data
- [ ] Test Mumbai region connectivity

**Migration:**
- [ ] Launch c5.xlarge in ap-south-1 (Mumbai)
- [ ] Install minimal LXDE desktop
- [ ] Configure optimized XRDP
- [ ] Install VS Code with RDP optimizations
- [ ] Create desktop shortcuts and panel

**Post-Migration:**
- [ ] Test RDP connection (should be <50ms latency)
- [ ] Verify VS Code functionality
- [ ] Test terminal emulator (lxterminal)
- [ ] Validate all shortcuts work
- [ ] Monitor resource usage (<40% CPU)

**Expected Results:**
- **Latency**: 10-50ms (vs 200-300ms from US-East-1)
- **Performance**: 70-80% improvement in responsiveness
- **Stability**: No more Ubuntu errors or terminal issues
- **Resource Usage**: Optimized for VS Code + 2 terminals

This setup will give you a clean, optimized environment specifically tailored for your development needs with VS Code and terminal access.

## 11. (Optional) Installing Additional Development Tools

You can install VS Code directly on the remote desktop.

```bash
# Via RDP Terminal or SSH
sudo apt update
sudo apt install -y wget gpg

# Add Microsoft GPG key
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/

# Add VS Code repository
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'

# Install VS Code
sudo apt update
sudo apt install -y code
```
