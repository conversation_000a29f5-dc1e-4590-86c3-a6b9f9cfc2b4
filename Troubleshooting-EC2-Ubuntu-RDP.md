# Guide: Configuring RDP and SSH on Ubuntu 24.04 EC2

This guide provides a comprehensive walkthrough for setting up an Ubuntu 24.04 EC2 instance with a graphical desktop environment (XFCE) accessible via RDP and standard SSH.

## 1. Prerequisites

### Local Machine (Windows)
- An SSH client (e.g., OpenSSH, PuTTY).
- Remote Desktop Connection client (`mstsc.exe`).
- Your AWS EC2 private key file (`.pem`).

### AWS
- An AWS account with permissions to create EC2 instances and manage security groups.
- An existing EC2 key pair or the ability to create a new one.

## 2. EC2 Instance Setup

1.  **Launch a new EC2 Instance** with the following configuration:
    *   **AMI:** Ubuntu Server 24.04 LTS
    *   **Instance Type:** `t3.medium` or higher is recommended for a GUI.
    *   **Key Pair:** Select your existing `.pem` key.
    *   **Security Group (Inbound Rules):**
        *   **SSH** (Port `22`) from your IP address.
        *   **RDP** (Port `3389`) from your IP address.

2.  **Allocate and Associate an Elastic IP:** To have a static public IP for your instance, allocate an Elastic IP and associate it with your newly created EC2 instance.

## 3. SSH Access

Connect to your instance using its public IP and your private key to perform the setup.

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

## 4. Desktop and RDP Server Installation

### Step 4.1: Update System and Install XFCE Desktop

Once connected via SSH, update your package manager and install the XFCE desktop environment.

```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y xfce4 xfce4-goodies
```

### Step 4.2: Install and Configure XRDP

Install the `xrdp` server, which provides the RDP functionality.

```bash
sudo apt install -y xrdp
sudo systemctl enable xrdp
sudo systemctl start xrdp
```

### Step 4.3: Set a Password for the `ubuntu` User

RDP requires a password for authentication. Set one for the default `ubuntu` user.

```bash
sudo passwd ubuntu
```
You will be prompted to enter and confirm a new password.

## 5. Final Configuration for RDP

This section contains the critical fixes derived from the troubleshooting session.

### Step 5.1: Configure the Session Manager

The `xrdp` session manager needs to know which desktop environment to start.

Create a `.xsession` file in the `ubuntu` user's home directory to explicitly start XFCE.

```bash
echo "xfce4-session" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession
```

### Step 5.2: Correct the `startwm.sh` Script

The `startwm.sh` script is the main entry point for an `xrdp` session. The default script can cause issues. Replace it with a more robust version that correctly handles session startup.

First, back up the original file:
```bash
sudo cp /etc/xrdp/startwm.sh /etc/xrdp/startwm.sh.backup
```

Now, replace its content:
```bash
sudo tee /etc/xrdp/startwm.sh > /dev/null << 'EOF'
#!/bin/sh
# xrdp X session start script

# Load profile scripts if they exist
if [ -r /etc/profile ]; then
    . /etc/profile
fi
if [ -r "$HOME/.profile" ]; then
    . "$HOME/.profile"
fi

# Unset variables that can interfere with the session
unset DBUS_SESSION_BUS_ADDRESS
unset XDG_RUNTIME_DIR

# Start the window manager
/etc/X11/Xsession
EOF
```
Make the new script executable:
```bash
sudo chmod +x /etc/xrdp/startwm.sh
```

### Step 5.3: Address `polkit` Issues

Authentication pop-ups for actions like color management or software updates can cause a black screen or session termination on RDP. Create a policy file to grant permissions and prevent this.

```bash
sudo bash -c 'cat > /etc/polkit-1/localauthority/50-local.d/45-allow-colord.pkla <<EOF
[Allow Colord all Users]
Identity=unix-user:*
Action=org.freedesktop.color-manager.create-device;org.freedesktop.color-manager.create-profile;org.freedesktop.color-manager.delete-device;org.freedesktop.color-manager.delete-profile;org.freedesktop.color-manager.modify-device;org.freedesktop.color-manager.modify-profile
ResultAny=no
ResultInactive=no
ResultActive=yes
EOF'
```

### Step 5.4: Restart Services

Apply all the changes by restarting the `xrdp` service.

```bash
sudo systemctl restart xrdp
```

## 6. Connecting via RDP

You should now be able to connect.

1.  Open the **Remote Desktop Connection** client on Windows (`mstsc`).
2.  Enter the **Public IP** of your EC2 instance.
3.  When prompted, use the following credentials:
    *   **Username:** `ubuntu`
    *   **Password:** The password you set in Step 4.3.

You should see the XFCE desktop environment.

## 7. Troubleshooting

If the RDP window closes immediately after login, the issue is almost always with the session manager failing to start the desktop.

**Key Log Files:**
- `/var/log/xrdp.log`
- `/var/log/xrdp-sesman.log`

**Common Error and Solution:**

- **Error in `xrdp-sesman.log`:**
  ```
  Window manager (pid XXXX, display 10) exited with non-zero exit code 127
  ```
  This means "command not found." The `startwm.sh` script cannot find the desktop session command.

- **Solution:**
  1.  Ensure XFCE is fully installed: `sudo apt install xfce4 xfce4-goodies`
  2.  Verify the `~/.xsession` file exists and contains `xfce4-session`.
  3.  Ensure the `/etc/xrdp/startwm.sh` script is correct, as detailed in Step 5.2.
  4.  Restart xrdp: `sudo systemctl restart xrdp`

## 8. (Optional) Installing VS Code

You can install VS Code directly on the remote desktop.

```bash
# Via RDP Terminal or SSH
sudo apt update
sudo apt install -y wget gpg

# Add Microsoft GPG key
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/

# Add VS Code repository
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'

# Install VS Code
sudo apt update
sudo apt install -y code
```
