# Guide: Configuring RDP and SSH on Ubuntu 24.04 EC2

This guide provides a comprehensive walkthrough for setting up an Ubuntu 24.04 EC2 instance with a graphical desktop environment (XFCE) accessible via RDP and standard SSH.

## 1. Prerequisites

### Local Machine (Windows)
- An SSH client (e.g., OpenSSH, PuTTY).
- Remote Desktop Connection client (`mstsc.exe`).
- Your AWS EC2 private key file (`.pem`).

### AWS
- An AWS account with permissions to create EC2 instances and manage security groups.
- An existing EC2 key pair or the ability to create a new one.

## 2. EC2 Instance Setup

1.  **Launch a new EC2 Instance** with the following configuration:
    *   **AMI:** Ubuntu Server 24.04 LTS
    *   **Instance Type:** `t3.medium` or higher is recommended for a GUI.
    *   **Key Pair:** Select your existing `.pem` key.
    *   **Security Group (Inbound Rules):**
        *   **SSH** (Port `22`) from your IP address.
        *   **RDP** (Port `3389`) from your IP address.

2.  **Allocate and Associate an Elastic IP:** To have a static public IP for your instance, allocate an Elastic IP and associate it with your newly created EC2 instance.

## 3. SSH Access

Connect to your instance using its public IP and your private key to perform the setup.

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

## 4. Desktop and RDP Server Installation

### Step 4.1: Update System and Install XFCE Desktop

Once connected via SSH, update your package manager and install the XFCE desktop environment.

```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y xfce4 xfce4-goodies
```

### Step 4.2: Install and Configure XRDP

Install the `xrdp` server, which provides the RDP functionality.

```bash
sudo apt install -y xrdp
sudo systemctl enable xrdp
sudo systemctl start xrdp
```

### Step 4.3: Set a Password for the `ubuntu` User

RDP requires a password for authentication. Set one for the default `ubuntu` user.

```bash
sudo passwd ubuntu
```
You will be prompted to enter and confirm a new password.

## 5. Final Configuration for RDP

This section contains the critical fixes derived from the troubleshooting session.

### Step 5.1: Configure the Session Manager

The `xrdp` session manager needs to know which desktop environment to start.

Create a `.xsession` file in the `ubuntu` user's home directory to explicitly start XFCE.

```bash
echo "xfce4-session" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession
```

### Step 5.2: Correct the `startwm.sh` Script

The `startwm.sh` script is the main entry point for an `xrdp` session. The default script can cause issues. Replace it with a more robust version that correctly handles session startup.

First, back up the original file:
```bash
sudo cp /etc/xrdp/startwm.sh /etc/xrdp/startwm.sh.backup
```

Now, replace its content:
```bash
sudo tee /etc/xrdp/startwm.sh > /dev/null << 'EOF'
#!/bin/sh
# xrdp X session start script

# Load profile scripts if they exist
if [ -r /etc/profile ]; then
    . /etc/profile
fi
if [ -r "$HOME/.profile" ]; then
    . "$HOME/.profile"
fi

# Unset variables that can interfere with the session
unset DBUS_SESSION_BUS_ADDRESS
unset XDG_RUNTIME_DIR

# Start the window manager
/etc/X11/Xsession
EOF
```
Make the new script executable:
```bash
sudo chmod +x /etc/xrdp/startwm.sh
```

### Step 5.3: Address `polkit` Issues

Authentication pop-ups for actions like color management or software updates can cause a black screen or session termination on RDP. Create a policy file to grant permissions and prevent this.

```bash
sudo bash -c 'cat > /etc/polkit-1/localauthority/50-local.d/45-allow-colord.pkla <<EOF
[Allow Colord all Users]
Identity=unix-user:*
Action=org.freedesktop.color-manager.create-device;org.freedesktop.color-manager.create-profile;org.freedesktop.color-manager.delete-device;org.freedesktop.color-manager.delete-profile;org.freedesktop.color-manager.modify-device;org.freedesktop.color-manager.modify-profile
ResultAny=no
ResultInactive=no
ResultActive=yes
EOF'
```

### Step 5.4: Restart Services

Apply all the changes by restarting the `xrdp` service.

```bash
sudo systemctl restart xrdp
```

## 6. Connecting via RDP

You should now be able to connect.

1.  Open the **Remote Desktop Connection** client on Windows (`mstsc`).
2.  Enter the **Public IP** of your EC2 instance.
3.  When prompted, use the following credentials:
    *   **Username:** `ubuntu`
    *   **Password:** The password you set in Step 4.3.

You should see the XFCE desktop environment.

## 7. Troubleshooting

If the RDP window closes immediately after login, the issue is almost always with the session manager failing to start the desktop.

**Key Log Files:**
- `/var/log/xrdp.log`
- `/var/log/xrdp-sesman.log`

**Common Error and Solution:**

- **Error in `xrdp-sesman.log`:**
  ```
  Window manager (pid XXXX, display 10) exited with non-zero exit code 127
  ```
  This means "command not found." The `startwm.sh` script cannot find the desktop session command.

- **Solution:**
  1.  Ensure XFCE is fully installed: `sudo apt install xfce4 xfce4-goodies`
  2.  Verify the `~/.xsession` file exists and contains `xfce4-session`.
  3.  Ensure the `/etc/xrdp/startwm.sh` script is correct, as detailed in Step 5.2.
  4.  Restart xrdp: `sudo systemctl restart xrdp`

## 8. Resetting RDP Password for Ubuntu User

If you've forgotten the RDP password that was set for the `ubuntu` user during initial configuration, you can reset it using SSH access. This section provides multiple methods to reset the password depending on your access situation.

### Method 1: Reset Password via SSH (Recommended)

This is the most straightforward method if you still have SSH access to your EC2 instance.

#### Step 8.1: Connect via SSH

Connect to your EC2 instance using your private key:

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

#### Step 8.2: Reset the Ubuntu User Password

Once connected via SSH, reset the password for the `ubuntu` user:

```bash
sudo passwd ubuntu
```

You will be prompted to:
1. Enter a new password
2. Confirm the new password

**Example output:**
```
New password: [enter your new password]
Retype new password: [confirm your new password]
passwd: password updated successfully
```

#### Step 8.3: Verify RDP Service Status

Ensure the RDP service is running properly:

```bash
sudo systemctl status xrdp
```

If it's not running, start it:

```bash
sudo systemctl start xrdp
sudo systemctl enable xrdp
```

#### Step 8.4: Test RDP Connection

1. Open **Remote Desktop Connection** on Windows (`mstsc`)
2. Enter your EC2 instance's public IP
3. Use the new credentials:
   - **Username:** `ubuntu`
   - **Password:** Your newly set password

### Method 2: Reset Password via AWS Systems Manager (Alternative)

If you have AWS Systems Manager Session Manager enabled, you can use it as an alternative to SSH.

#### Step 8.1: Enable Session Manager (if not already enabled)

1. In AWS Console, go to **EC2** → **Instances**
2. Select your instance
3. Ensure it has an IAM role with `AmazonSSMManagedInstanceCore` policy
4. If not, create and attach the role:

```bash
# Create IAM role (via AWS CLI)
aws iam create-role --role-name EC2-SSM-Role --assume-role-policy-document '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}'

# Attach policy
aws iam attach-role-policy --role-name EC2-SSM-Role --policy-arn arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore

# Create instance profile
aws iam create-instance-profile --instance-profile-name EC2-SSM-Profile
aws iam add-role-to-instance-profile --instance-profile-name EC2-SSM-Profile --role-name EC2-SSM-Role
```

#### Step 8.2: Connect via Session Manager

1. In AWS Console, go to **Systems Manager** → **Session Manager**
2. Click **Start session**
3. Select your EC2 instance
4. Click **Start session**

#### Step 8.3: Reset Password

In the Session Manager terminal:

```bash
sudo passwd ubuntu
```

### Method 3: Reset Password via EC2 User Data (Advanced)

If you don't have SSH access and Session Manager isn't available, you can use EC2 User Data to reset the password on next boot.

#### Step 8.1: Stop the Instance

1. In AWS Console, go to **EC2** → **Instances**
2. Select your instance
3. Click **Instance state** → **Stop instance**

#### Step 8.2: Modify User Data

1. Right-click the stopped instance → **Instance settings** → **Edit user data**
2. Add the following script:

```bash
#!/bin/bash
# Reset ubuntu user password
echo 'ubuntu:YourNewPassword123!' | chpasswd
# Remove this user data after execution (optional)
# echo "" > /var/lib/cloud/instance/user-data.txt
```

**⚠️ Security Note:** Replace `YourNewPassword123!` with your desired password. Be aware that user data is visible in the EC2 console.

#### Step 8.3: Start the Instance

1. Click **Instance state** → **Start instance**
2. Wait for the instance to boot completely
3. The password will be reset during the boot process

#### Step 8.4: Clear User Data (Security Best Practice)

After the password is reset:
1. Stop the instance again
2. Edit user data and clear the content
3. Start the instance

### Method 4: Create New User with RDP Access (Alternative Solution)

If you prefer to create a new user instead of resetting the existing password:

#### Step 4.1: Connect via SSH

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

#### Step 4.2: Create New User

```bash
# Create new user
sudo adduser newrdpuser

# Add user to sudo group (optional)
sudo usermod -aG sudo newrdpuser

# Set up RDP session for new user
echo "xfce4-session" | sudo tee /home/<USER>/.xsession
sudo chown newrdpuser:newrdpuser /home/<USER>/.xsession
```

#### Step 4.3: Test New User RDP Access

Use the new username and password for RDP connection.

### Password Security Best Practices

#### Strong Password Requirements
- **Minimum 12 characters**
- **Mix of uppercase, lowercase, numbers, and symbols**
- **Avoid common words or patterns**
- **Example format:** `MySecure2024!RDP`

#### Additional Security Measures

1. **Enable SSH Key Authentication Only** (disable password auth for SSH):
   ```bash
   sudo nano /etc/ssh/sshd_config
   # Set: PasswordAuthentication no
   sudo systemctl restart ssh
   ```

2. **Restrict RDP Access by IP**:
   - In AWS Security Group, limit RDP (port 3389) to specific IP addresses
   - Avoid using `0.0.0.0/0` (anywhere)

3. **Enable AWS CloudTrail** for audit logging:
   ```bash
   # Monitor RDP login attempts in CloudWatch logs
   sudo tail -f /var/log/xrdp.log
   ```

4. **Regular Password Rotation**:
   - Change RDP password every 90 days
   - Document password changes in your security log

### Troubleshooting Password Reset Issues

#### Issue 1: "Authentication failure" after password reset

**Solution:**
```bash
# Restart xrdp service
sudo systemctl restart xrdp

# Check service status
sudo systemctl status xrdp

# Verify user exists and has password set
sudo getent passwd ubuntu
```

#### Issue 2: User data script didn't execute

**Solution:**
```bash
# Check cloud-init logs
sudo cat /var/log/cloud-init-output.log
sudo cat /var/log/cloud-init.log

# Manually run user data script
sudo bash /var/lib/cloud/instance/user-data.txt
```

#### Issue 3: Session Manager not working

**Solution:**
```bash
# Install SSM agent (if missing)
sudo snap install amazon-ssm-agent --classic
sudo systemctl start snap.amazon-ssm-agent.amazon-ssm-agent.service
sudo systemctl enable snap.amazon-ssm-agent.amazon-ssm-agent.service
```

### Verification Checklist

After resetting the password, verify the following:

- [ ] RDP service is running: `sudo systemctl status xrdp`
- [ ] User password is set: `sudo passwd -S ubuntu`
- [ ] Security group allows RDP (port 3389) from your IP
- [ ] EC2 instance is running and accessible
- [ ] XFCE desktop environment is installed
- [ ] `.xsession` file exists in user's home directory
- [ ] Can connect via RDP with new credentials

## 9. (Optional) Installing VS Code

You can install VS Code directly on the remote desktop.

```bash
# Via RDP Terminal or SSH
sudo apt update
sudo apt install -y wget gpg

# Add Microsoft GPG key
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/

# Add VS Code repository
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'

# Install VS Code
sudo apt update
sudo apt install -y code
```
