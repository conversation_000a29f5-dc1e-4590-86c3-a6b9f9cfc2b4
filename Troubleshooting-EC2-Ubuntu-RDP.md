# Complete Guide: Ubuntu 24.04 RDP Setup with LXDE Desktop on AWS EC2 Mumbai

This guide provides a systematic, step-by-step approach for setting up an Ubuntu 24.04 EC2 instance in Mumbai region with LXDE desktop environment accessible via RDP, optimized for development work with VS Code and terminal access.

## Table of Contents
1. [Prerequisites](#1-prerequisites)
2. [AWS EC2 Instance Setup in Mumbai](#2-aws-ec2-instance-setup-in-mumbai)
3. [Initial Server Configuration](#3-initial-server-configuration)
4. [LXDE Desktop Environment Installation](#4-lxde-desktop-environment-installation)
5. [XRDP Installation and Configuration](#5-xrdp-installation-and-configuration)
6. [User Account and Password Setup](#6-user-account-and-password-setup)
7. [LXDE Optimization for RDP](#7-lxde-optimization-for-rdp)
8. [Development Tools Installation](#8-development-tools-installation)
9. [RDP Connection Testing](#9-rdp-connection-testing)
10. [Performance Optimization](#10-performance-optimization)
11. [Troubleshooting](#11-troubleshooting)

---

## 1. Prerequisites

### 1.1 Local Machine Requirements (Windows)
- **Remote Desktop Connection** client (`mstsc.exe`) - Built into Windows
- **SSH client** - Windows PowerShell (built-in) or PuTTY
- **AWS CLI** (optional) - For command-line management
- **Your AWS EC2 private key file** (`.pem` format)

### 1.2 AWS Account Requirements
- **AWS account** with EC2 permissions
- **Existing EC2 key pair** or ability to create new one
- **VPC and Security Group** management permissions
- **Elastic IP** allocation permissions (recommended)

### 1.3 Network Requirements
- **Stable internet connection** from India
- **Public IP access** to AWS Mumbai region
- **RDP client** capable of handling 16-bit color depth

---

## 2. AWS EC2 Instance Setup in Mumbai

### 2.1 Launch EC2 Instance in ap-south-1 (Mumbai)

#### Step 2.1.1: Access AWS Console
1. **Login to AWS Console**: https://console.aws.amazon.com
2. **Select Region**: Choose **Asia Pacific (Mumbai) ap-south-1**
3. **Navigate to EC2**: Services → EC2 → Instances

#### Step 2.1.2: Launch New Instance
**Click "Launch Instance" and configure:**

**Basic Configuration:**
- **Name**: `Ubuntu-RDP-Mumbai-Dev`
- **AMI**: Ubuntu Server 24.04 LTS (Free tier eligible)
- **Architecture**: 64-bit (x86)

**Instance Type Selection:**
```
Recommended for Development with VS Code + Terminals:
- c5.xlarge   (4 vCPU, 8 GB RAM)  - Best performance
- m5.large    (2 vCPU, 8 GB RAM)  - Good balance
- t3a.large   (2 vCPU, 8 GB RAM)  - Budget option
```

**Key Pair Configuration:**
- **Select existing key pair** OR **Create new key pair**
- **Download `.pem` file** and store securely
- **Note**: Keep this file safe - required for SSH access

#### Step 2.1.3: Network Settings Configuration

**VPC and Subnet:**
- **VPC**: Default VPC (or your custom VPC)
- **Subnet**: Any public subnet in ap-south-1
- **Auto-assign Public IP**: Enable

**Security Group Configuration:**
Create new security group with these rules:

| Type | Protocol | Port Range | Source | Description |
|------|----------|------------|---------|-------------|
| SSH | TCP | 22 | Your IP/32 | SSH access |
| RDP | TCP | 3389 | Your IP/32 | Remote Desktop |
| HTTP | TCP | 80 | 0.0.0.0/0 | Web access (optional) |
| HTTPS | TCP | 443 | 0.0.0.0/0 | Secure web (optional) |

**⚠️ Security Note**: Replace "Your IP" with your actual public IP address. Avoid using 0.0.0.0/0 for SSH and RDP.

#### Step 2.1.4: Storage Configuration
- **Root Volume**: 30 GB GP3 SSD
- **Volume Type**: gp3 (better performance than gp2)
- **IOPS**: 3000 (default)
- **Throughput**: 125 MB/s (default)

#### Step 2.1.5: Launch Instance
1. **Review configuration** summary
2. **Click "Launch Instance"**
3. **Note the Instance ID** for reference
4. **Wait for instance state**: Running

### 2.2 Elastic IP Configuration (Recommended)

#### Step 2.2.1: Allocate Elastic IP
1. **Navigate**: EC2 → Network & Security → Elastic IPs
2. **Click "Allocate Elastic IP address"**
3. **Select**: Amazon's pool of IPv4 addresses
4. **Click "Allocate"**

#### Step 2.2.2: Associate Elastic IP
1. **Select allocated Elastic IP**
2. **Actions → Associate Elastic IP address**
3. **Instance**: Select your Ubuntu instance
4. **Click "Associate"**

**Benefits of Elastic IP:**
- **Static IP address** - doesn't change on reboot
- **Consistent RDP connection** - same IP every time
- **DNS mapping** - can create custom domain if needed

---

## 3. Initial Server Configuration

### 3.1 SSH Connection to Mumbai Instance

#### Step 3.1.1: Prepare SSH Connection
**On Windows (PowerShell):**
```powershell
# Navigate to directory containing your .pem file
cd C:\path\to\your\key\

# Set correct permissions (Windows)
icacls "your-key.pem" /inheritance:r /grant:r "%username%:R"
```

**On Linux/macOS:**
```bash
# Set correct permissions
chmod 400 your-key.pem
```

#### Step 3.1.2: Connect via SSH
```bash
# Replace with your actual Elastic IP and key file name
ssh -i "your-key.pem" ubuntu@your-elastic-ip

# Example:
# ssh -i "mumbai-dev-key.pem" <EMAIL>
```

**Expected Output:**
```
Welcome to Ubuntu 24.04 LTS (GNU/Linux 6.8.0-1009-aws x86_64)
ubuntu@ip-172-31-xx-xx:~$
```

### 3.2 System Update and Preparation

#### Step 3.2.1: Update Package Repository
```bash
# Update package lists
sudo apt update

# Upgrade existing packages
sudo apt upgrade -y

# Install essential utilities
sudo apt install -y \
    curl \
    wget \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release
```

#### Step 3.2.2: System Information Verification
```bash
# Verify system information
echo "=== System Information ==="
echo "OS Version: $(lsb_release -d | cut -f2)"
echo "Kernel: $(uname -r)"
echo "Architecture: $(uname -m)"
echo "Instance Type: $(curl -s http://***************/latest/meta-data/instance-type)"
echo "Region: $(curl -s http://***************/latest/meta-data/placement/region)"
echo "CPU Cores: $(nproc)"
echo "Memory: $(free -h | awk 'NR==2{print $2}')"
echo "Disk Space: $(df -h / | awk 'NR==2{print $4}') available"
```

---

## 4. LXDE Desktop Environment Installation

### 4.1 Why LXDE for RDP Development

**LXDE Benefits:**
- **Ultra-lightweight**: 70% less resource usage than XFCE/GNOME
- **RDP optimized**: Minimal visual effects that cause RDP lag
- **Development friendly**: Perfect for VS Code + terminal workflow
- **Stable**: Fewer components = fewer potential issues
- **Fast startup**: Quick session initialization

### 4.2 Install LXDE Desktop Components

#### Step 4.2.1: Install Core LXDE Components
```bash
# Install minimal LXDE desktop environment
sudo apt install -y \
    lxde-core \
    lxde-common \
    lxterminal \
    pcmanfm \
    lxpanel \
    openbox \
    obconf \
    lxappearance \
    lxsession

# Install essential desktop utilities
sudo apt install -y \
    firefox \
    gedit \
    file-roller \
    gvfs \
    gvfs-backends \
    gvfs-fuse \
    network-manager-gnome
```

#### Step 4.2.2: Install Additional Utilities
```bash
# Install useful applications for development
sudo apt install -y \
    git \
    vim \
    nano \
    htop \
    tree \
    unzip \
    zip \
    curl \
    wget \
    net-tools
```

#### Step 4.2.3: Verify LXDE Installation
```bash
# Check installed LXDE components
dpkg -l | grep -E "(lxde|openbox|pcmanfm|lxterminal)"

# Verify desktop environment
echo $XDG_CURRENT_DESKTOP
ls /usr/share/xsessions/
```

---

## 5. XRDP Installation and Configuration

### 5.1 Install XRDP Server

#### Step 5.1.1: Install XRDP Package
```bash
# Install XRDP server
sudo apt install -y xrdp

# Check XRDP version
xrdp --version
```

#### Step 5.1.2: Enable XRDP Service
```bash
# Enable XRDP to start on boot
sudo systemctl enable xrdp

# Start XRDP service
sudo systemctl start xrdp

# Check service status
sudo systemctl status xrdp
```

**Expected Output:**
```
● xrdp.service - xrdp daemon
     Loaded: loaded (/lib/systemd/system/xrdp.service; enabled; vendor preset: enabled)
     Active: active (running)
```

### 5.2 Configure XRDP for LXDE

#### Step 5.2.1: Create Optimized XRDP Configuration
```bash
# Backup original configuration
sudo cp /etc/xrdp/xrdp.ini /etc/xrdp/xrdp.ini.backup

# Create optimized XRDP configuration for LXDE
sudo tee /etc/xrdp/xrdp.ini > /dev/null << 'EOF'
[Globals]
ini_version=1
fork=true
port=3389
tcp_nodelay=true
tcp_keepalive=true
security_layer=rdp
crypt_level=high
certificate=
key_file=
ssl_protocols=TLSv1.2, TLSv1.3
autorun=
allow_channels=true
allow_multimon=false
bitmap_cache=true
bitmap_compression=true
bulk_compression=true
max_bpp=24
new_cursors=true
use_fastpath=both
require_credentials=true
login_retry=4
killloggedon=false
rdp_keepalive=true

[Xorg]
name=Xorg
lib=libxup.so
username=ask
password=ask
ip=127.0.0.1
port=-1
code=20
EOF
```

#### Step 5.2.2: Configure Session Manager
```bash
# Backup original sesman configuration
sudo cp /etc/xrdp/sesman.ini /etc/xrdp/sesman.ini.backup

# Configure session manager for LXDE
sudo tee /etc/xrdp/sesman.ini > /dev/null << 'EOF'
[Globals]
ListenAddress=127.0.0.1
ListenPort=3350
EnableUserWindowManager=true
UserWindowManager=startlxde
DefaultWindowManager=startlxde
ReconnectSh=/etc/xrdp/reconnectwm.sh

[Security]
AllowRootLogin=false
MaxLoginRetry=4
TerminalServerUsers=tsusers
TerminalServerAdmins=tsadmins
AlwaysGroupCheck=false

[Sessions]
X11DisplayOffset=10
MaxSessions=50
KillDisconnected=false
IdleTimeLimit=0
DisconnectedTimeLimit=0
Policy=Default

[Logging]
LogFile=xrdp-sesman.log
LogLevel=INFO
EnableSyslog=true
SyslogLevel=INFO

[Xorg]
param1=-bs
param2=-ac
param3=-nolisten
param4=tcp
param5=-dpi
param6=96
EOF
```

---

## 6. User Account and Password Setup

### 6.1 Configure Ubuntu User for RDP Access

#### Step 6.1.1: Set Password for Ubuntu User
RDP requires password authentication. Set a strong password for the ubuntu user:

```bash
# Set password for ubuntu user
sudo passwd ubuntu
```

**Password Requirements:**
- **Minimum 12 characters**
- **Mix of uppercase, lowercase, numbers, symbols**
- **Example format**: `MySecure2024!RDP`

**You will see:**
```
New password: [enter your password]
Retype new password: [confirm your password]
passwd: password updated successfully
```

#### Step 6.1.2: Configure User Session for LXDE
```bash
# Create .xsession file to specify LXDE as desktop environment
echo "startlxde" | tee /home/<USER>/.xsession

# Set correct ownership
sudo chown ubuntu:ubuntu /home/<USER>/.xsession

# Make it executable
chmod +x /home/<USER>/.xsession

# Verify the file
cat /home/<USER>/.xsession
```

#### Step 6.1.3: Add User to Required Groups
```bash
# Add ubuntu user to necessary groups for RDP and system access
sudo usermod -aG sudo ubuntu
sudo usermod -aG adm ubuntu
sudo usermod -aG dialout ubuntu
sudo usermod -aG cdrom ubuntu
sudo usermod -aG floppy ubuntu
sudo usermod -aG audio ubuntu
sudo usermod -aG dip ubuntu
sudo usermod -aG video ubuntu
sudo usermod -aG plugdev ubuntu
sudo usermod -aG netdev ubuntu

# Verify group membership
groups ubuntu
```

---

## 7. LXDE Optimization for RDP

### 7.1 Configure LXDE for Optimal RDP Performance

#### Step 7.1.1: Create LXDE Configuration Directory
```bash
# Create LXDE configuration directories
mkdir -p /home/<USER>/.config/lxsession/LXDE
mkdir -p /home/<USER>/.config/lxterminal
mkdir -p /home/<USER>/.config/pcmanfm/LXDE
mkdir -p /home/<USER>/.config/openbox
mkdir -p /home/<USER>/.config/lxpanel/LXDE/panels

# Set ownership
sudo chown -R ubuntu:ubuntu /home/<USER>/.config
```

#### Step 7.1.2: Configure LXDE Session Settings
```bash
# Create optimized LXDE session configuration
cat > /home/<USER>/.config/lxsession/LXDE/desktop.conf << 'EOF'
[Session]
window_manager=openbox-lxde
windows_manager/command=openbox
windows_manager/session=LXDE
disable_autostart=no
polkit/command=lxpolkit
clipboard/command=lxclipboard
xsettings_manager/command=build-in
proxy_manager/command=build-in
keyring/command=ssh-agent
quit_manager/command=lxsession-logout
quit_manager/image=/usr/share/lxde/images/logout-banner.png
quit_manager/layout=top

[GTK]
sNet/ThemeName=Adwaita
sNet/IconThemeName=nuoveXT2
sGtk/FontName=Sans 10
iGtk/ToolbarStyle=3
iGtk/ButtonImages=1
iGtk/MenuImages=1
iGtk/CursorThemeSize=18
iXft/Antialias=0
iXft/Hinting=0
sXft/HintStyle=hintnone
sXft/RGBA=none

[Mouse]
AccFactor=20
AccThreshold=10
LeftHanded=0

[Keyboard]
Delay=500
Interval=30
Beep=1

[State]
guess_default=true

[Dbus]
lxde=true

[Environment]
menu_prefix=lxde-
EOF
```

#### Step 7.1.3: Configure LXTerminal for Development
```bash
# Create optimized terminal configuration
cat > /home/<USER>/.config/lxterminal/lxterminal.conf << 'EOF'
[general]
fontname=Monospace 11
selchars=-A-Za-z0-9,./?%&#:_=+@~
scrollback=1000
bgcolor=rgb(0,0,0)
fgcolor=rgb(255,255,255)
palette_color_0=rgb(0,0,0)
palette_color_1=rgb(178,24,24)
palette_color_2=rgb(24,178,24)
palette_color_3=rgb(178,104,24)
palette_color_4=rgb(24,24,178)
palette_color_5=rgb(178,24,178)
palette_color_6=rgb(24,178,178)
palette_color_7=rgb(178,178,178)
palette_color_8=rgb(104,104,104)
palette_color_9=rgb(255,84,84)
palette_color_10=rgb(84,255,84)
palette_color_11=rgb(255,255,84)
palette_color_12=rgb(84,84,255)
palette_color_13=rgb(255,84,255)
palette_color_14=rgb(84,255,255)
palette_color_15=rgb(255,255,255)
color_preset=Custom
disallowbold=false
cursorblinks=false
cursorunderline=false
audiblebell=false
tabpos=top
geometry_columns=80
geometry_rows=24
hidescrollbar=false
hidemenubar=false
hideclosebutton=false
hidepointer=false
EOF
```

#### Step 7.1.4: Configure File Manager (PCManFM)
```bash
# Create file manager configuration
cat > /home/<USER>/.config/pcmanfm/LXDE/pcmanfm.conf << 'EOF'
[config]
bm_open_method=0
su_cmd=gksu %s
terminal=lxterminal
archiver=file-roller

[volume]
mount_on_startup=1
mount_removable=1
autorun=1

[ui]
always_show_tabs=0
max_tab_chars=32
win_width=800
win_height=600
splitter_pos=200
media_in_new_tab=0
desktop_folder_new_win=0
change_tab_on_drop=1
close_on_unmount=1
focus_previous=0
side_pane_mode=places
view_mode=icon_view
show_hidden=0
sort=name;ascending;
toolbar=newtab;navigation;home;
show_statusbar=1
pathbar_mode_buttons=0
EOF
```

### 7.2 Optimize System for RDP Performance

#### Step 7.2.1: Disable Unnecessary Services
```bash
# Disable services that consume resources unnecessarily
sudo systemctl disable bluetooth
sudo systemctl disable cups
sudo systemctl disable avahi-daemon
sudo systemctl disable ModemManager
sudo systemctl disable snapd

# Stop services immediately
sudo systemctl stop bluetooth
sudo systemctl stop cups
sudo systemctl stop avahi-daemon
sudo systemctl stop ModemManager
sudo systemctl stop snapd
```

#### Step 7.2.2: Create Performance Optimization Script
```bash
# Create script to optimize display and performance
cat > /home/<USER>/optimize-rdp-performance.sh << 'EOF'
#!/bin/bash
# RDP Performance Optimization Script

echo "Applying RDP performance optimizations..."

# Disable screen saver and power management
xset s off
xset -dpms
xset s noblank

# Optimize font rendering for RDP
echo "Xft.antialias: 0" >> ~/.Xresources
echo "Xft.hinting: 0" >> ~/.Xresources
echo "Xft.hintstyle: hintnone" >> ~/.Xresources
echo "Xft.rgba: none" >> ~/.Xresources
xrdb ~/.Xresources

# Set simple window manager theme
if command -v openbox >/dev/null 2>&1; then
    mkdir -p ~/.config/openbox
    echo '<?xml version="1.0" encoding="UTF-8"?>
<openbox_config xmlns="http://openbox.org/3.4/rc">
  <theme>
    <name>Clearlooks</name>
    <titleLayout>NLIMC</titleLayout>
    <keepBorder>yes</keepBorder>
    <animateIconify>no</animateIconify>
    <font place="ActiveWindow">
      <name>sans</name>
      <size>8</size>
      <weight>bold</weight>
      <slant>normal</slant>
    </font>
    <font place="InactiveWindow">
      <name>sans</name>
      <size>8</size>
      <weight>bold</weight>
      <slant>normal</slant>
    </font>
  </theme>
</openbox_config>' > ~/.config/openbox/rc.xml
fi

echo "RDP performance optimizations applied!"
EOF

# Make script executable
chmod +x /home/<USER>/optimize-rdp-performance.sh

# Add to session startup
echo "/home/<USER>/optimize-rdp-performance.sh" >> /home/<USER>/.xsession
```

#### Step 7.2.3: Configure Startup Applications
```bash
# Create autostart directory
mkdir -p /home/<USER>/.config/autostart

# Disable unnecessary startup applications
cat > /home/<USER>/.config/autostart/disable-screensaver.desktop << 'EOF'
[Desktop Entry]
Type=Application
Name=Disable Screensaver
Exec=xset s off -dpms
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
EOF

# Set ownership
sudo chown -R ubuntu:ubuntu /home/<USER>/.config
```

---

## 8. Development Tools Installation

### 8.1 Install VS Code for Development

#### Step 8.1.1: Install VS Code via Official Repository
```bash
# Install prerequisites
sudo apt update
sudo apt install -y wget gpg software-properties-common

# Add Microsoft GPG key
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/

# Add VS Code repository
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'

# Update package list and install VS Code
sudo apt update
sudo apt install -y code

# Verify installation
code --version
```

#### Step 8.1.2: Configure VS Code for RDP Performance
```bash
# Create VS Code settings directory
mkdir -p /home/<USER>/.config/Code/User

# Create optimized settings for RDP
cat > /home/<USER>/.config/Code/User/settings.json << 'EOF'
{
    "window.titleBarStyle": "custom",
    "window.menuBarVisibility": "toggle",
    "editor.fontFamily": "monospace",
    "editor.fontSize": 14,
    "editor.renderWhitespace": "none",
    "editor.minimap.enabled": false,
    "editor.smoothScrolling": false,
    "editor.cursorBlinking": "solid",
    "editor.cursorSmoothCaretAnimation": "off",
    "workbench.activityBar.visible": true,
    "workbench.statusBar.visible": true,
    "workbench.sideBar.location": "left",
    "workbench.editor.enablePreview": false,
    "workbench.startupEditor": "newUntitledFile",
    "workbench.list.smoothScrolling": false,
    "workbench.reduceMotion": "on",
    "workbench.colorTheme": "Default Dark+",
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "terminal.integrated.fontFamily": "monospace",
    "terminal.integrated.fontSize": 12,
    "terminal.integrated.smoothScrolling": false,
    "window.dialogStyle": "custom",
    "workbench.tree.renderIndentGuides": "none",
    "editor.accessibilitySupport": "off",
    "workbench.enableExperiments": false,
    "extensions.autoUpdate": false,
    "update.mode": "manual",
    "telemetry.telemetryLevel": "off"
}
EOF

# Set ownership
sudo chown -R ubuntu:ubuntu /home/<USER>/.config/Code
```

### 8.2 Install Development Tools and Languages

#### Step 8.2.1: Install Programming Languages
```bash
# Install Python development environment
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev

# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt install -y nodejs

# Install Java Development Kit
sudo apt install -y default-jdk

# Install build tools
sudo apt install -y \
    build-essential \
    cmake \
    make \
    gcc \
    g++

# Verify installations
echo "=== Development Tools Verification ==="
echo "Python: $(python3 --version)"
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "Java: $(java -version 2>&1 | head -1)"
echo "GCC: $(gcc --version | head -1)"
```

#### Step 8.2.2: Install Additional Development Utilities
```bash
# Install version control and utilities
sudo apt install -y \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    curl \
    wget \
    unzip \
    zip \
    net-tools \
    dnsutils

# Configure Git (replace with your details)
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main
```

### 8.3 Create Desktop Shortcuts and Quick Access

#### Step 8.3.1: Create Desktop Shortcuts
```bash
# Create desktop directory
mkdir -p /home/<USER>/Desktop

# Create VS Code desktop shortcut
cat > /home/<USER>/Desktop/VSCode.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Visual Studio Code
Comment=Code Editing. Redefined.
Exec=/usr/bin/code
Icon=code
Terminal=false
Categories=Development;IDE;
StartupWMClass=Code
EOF

# Create Terminal shortcut
cat > /home/<USER>/Desktop/Terminal.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=LXTerminal
Comment=Terminal Emulator
Exec=lxterminal
Icon=terminal
Terminal=false
Categories=System;TerminalEmulator;
EOF

# Create File Manager shortcut
cat > /home/<USER>/Desktop/FileManager.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=File Manager
Comment=File Manager
Exec=pcmanfm
Icon=file-manager
Terminal=false
Categories=System;FileManager;
EOF

# Make shortcuts executable
chmod +x /home/<USER>/Desktop/*.desktop

# Set ownership
sudo chown -R ubuntu:ubuntu /home/<USER>/Desktop
```

#### Step 8.3.2: Configure LXDE Panel with Quick Launch
```bash
# Create optimized panel configuration with development tools
cat > /home/<USER>/.config/lxpanel/LXDE/panels/panel << 'EOF'
# lxpanel <profile> config file. Manually editing is not recommended.
# Use preference dialog in lxpanel to adjust config when you can.

Global {
    edge=bottom
    allign=left
    margin=0
    widthtype=percent
    width=100
    height=28
    transparent=0
    tintcolor=#000000
    alpha=0
    setdocktype=1
    setpartialstrut=1
    autohide=0
    heightwhenhidden=2
    usefontcolor=1
    fontcolor=#ffffff
    background=1
    backgroundfile=/usr/share/lxpanel/images/background.png
}

Plugin {
    type=menu
    Config {
        image=/usr/share/pixmaps/ubuntu-logo.png
        system {
        }
        separator {
        }
        item {
            command=run
        }
        separator {
        }
        item {
            image=gnome-logout
            command=logout
        }
    }
}

Plugin {
    type=launchbar
    Config {
        Button {
            id=lxterminal.desktop
        }
        Button {
            id=code.desktop
        }
        Button {
            id=pcmanfm.desktop
        }
        Button {
            id=firefox.desktop
        }
    }
}

Plugin {
    type=taskbar
    expand=1
    Config {
        tooltips=1
        IconsOnly=0
        ShowAllDesks=0
        UseMouseWheel=1
        UseUrgencyHint=1
        FlatButton=0
        MaxTaskWidth=200
        spacing=1
    }
}

Plugin {
    type=tray
}

Plugin {
    type=dclock
    Config {
        ClockFmt=%R
        TooltipFmt=%A %x
        BoldFont=0
        IconOnly=0
        CenterText=0
    }
}
EOF
```

---

## 9. RDP Connection Testing

### 9.1 Final System Configuration

#### Step 9.1.1: Restart All Services
```bash
# Restart XRDP service to apply all configurations
sudo systemctl restart xrdp

# Verify service status
sudo systemctl status xrdp

# Check if XRDP is listening on port 3389
sudo netstat -tlnp | grep :3389
```

#### Step 9.1.2: Verify LXDE Configuration
```bash
# Check LXDE session configuration
cat /home/<USER>/.xsession

# Verify desktop environment files
ls -la /usr/share/xsessions/

# Test LXDE components
which startlxde
which lxterminal
which pcmanfm
```

### 9.2 Connect via RDP from Windows

#### Step 9.2.1: Open Remote Desktop Connection
1. **Press Windows Key + R**
2. **Type**: `mstsc` and press Enter
3. **Remote Desktop Connection** window opens

#### Step 9.2.2: Configure Connection Settings
**Computer Tab:**
- **Computer**: Enter your **Elastic IP address**
- **User name**: `ubuntu`
- **Check**: "Allow me to save credentials"

**Display Tab:**
- **Remote desktop size**: Choose appropriate size
- **Colors**: **High Color (16 bit)** (for better RDP performance)
- **Uncheck**: "Display the connection bar when I use the full screen"

**Local Resources Tab:**
- **Remote computer sound**: **Do not play**
- **Keyboard**: **On this computer**
- **Local devices and resources**:
  - **Uncheck**: Printers (unless needed)
  - **Check**: Clipboard (for copy/paste)

**Experience Tab:**
- **Choose your connection speed**: **LAN (10 Mbps or higher)**
- **Performance options** - **Uncheck all** for better performance:
  - Desktop background
  - Font smoothing
  - Desktop composition
  - Show contents of windows while dragging
  - Menu and window animation
  - Themes

#### Step 9.2.3: Connect to Ubuntu Instance
1. **Click "Connect"**
2. **Enter credentials**:
   - **Username**: `ubuntu`
   - **Password**: [Password you set earlier]
3. **Click "OK"**

**Expected Result:**
- **LXDE desktop** should appear
- **Panel at bottom** with application launchers
- **Desktop shortcuts** for VS Code, Terminal, File Manager

#### Step 9.2.4: Test Desktop Environment
```bash
# Once connected via RDP, test these applications:

# 1. Open Terminal
# Click Terminal icon in panel or desktop

# 2. Test VS Code
# Click VS Code icon or run from terminal:
code

# 3. Test File Manager
# Click File Manager icon or run:
pcmanfm

# 4. Test system information
neofetch
# or
hostnamectl
```

### 9.3 Performance Verification

#### Step 9.3.1: Test Network Latency
```bash
# From your local Windows machine, test latency to Mumbai
ping your-elastic-ip

# Expected results from India:
# Mumbai (ap-south-1): 10-50ms
# Much better than US-East-1: 200-300ms
```

#### Step 9.3.2: Monitor System Resources
```bash
# In RDP session terminal, monitor resources:

# CPU and Memory usage
htop

# Disk usage
df -h

# Network connections
ss -tuln | grep :3389

# XRDP processes
ps aux | grep xrdp
```

---

## 10. Performance Optimization

### 10.1 System Performance Tuning

#### Step 10.1.1: Optimize Network Parameters
```bash
# Create network optimization script
sudo tee /etc/sysctl.d/99-rdp-optimization.conf > /dev/null << 'EOF'
# Network optimizations for RDP performance
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1
EOF

# Apply network optimizations
sudo sysctl -p /etc/sysctl.d/99-rdp-optimization.conf
```

#### Step 10.1.2: Create Performance Monitoring Script
```bash
# Create system monitoring script
cat > /home/<USER>/monitor-performance.sh << 'EOF'
#!/bin/bash

echo "=== RDP Performance Monitor ==="
echo "Date: $(date)"
echo ""

echo "=== System Resources ==="
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}')"
echo "Memory Usage: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2 }')"
echo "Disk Usage: $(df -h / | awk 'NR==2{print $5}')"
echo ""

echo "=== Network Performance ==="
echo "Instance Region: $(curl -s http://***************/latest/meta-data/placement/region)"
echo "Instance Type: $(curl -s http://***************/latest/meta-data/instance-type)"
echo ""

echo "=== RDP Service Status ==="
echo "XRDP Status: $(systemctl is-active xrdp)"
echo "XRDP Connections: $(ss -tn | grep :3389 | wc -l)"
echo ""

echo "=== Desktop Environment ==="
echo "Current Desktop: $XDG_CURRENT_DESKTOP"
echo "Session Type: $XDG_SESSION_TYPE"
echo "Display: $DISPLAY"
echo ""

echo "=== Running Processes (Top 5 CPU) ==="
ps aux --sort=-%cpu | head -6
EOF

chmod +x /home/<USER>/monitor-performance.sh
```

### 10.2 Automated Optimization

#### Step 10.2.1: Create Startup Optimization Script
```bash
# Create comprehensive startup optimization
cat > /home/<USER>/startup-optimization.sh << 'EOF'
#!/bin/bash
# Comprehensive RDP startup optimization

# Wait for desktop to load
sleep 5

# Disable unnecessary visual effects
xset s off -dpms s noblank

# Optimize font rendering
xrdb -merge << XRDB_EOF
Xft.antialias: 0
Xft.hinting: 0
Xft.hintstyle: hintnone
Xft.rgba: none
XRDB_EOF

# Set simple GTK theme
export GTK_THEME=Adwaita

# Disable animations in applications
export DESKTOP_SESSION=LXDE
export XDG_CURRENT_DESKTOP=LXDE

# Log optimization completion
echo "$(date): RDP optimization completed" >> /home/<USER>/rdp-optimization.log
EOF

chmod +x /home/<USER>/startup-optimization.sh

# Add to autostart
echo "/home/<USER>/startup-optimization.sh" >> /home/<USER>/.config/autostart/rdp-optimization.desktop
```

---

## 11. Troubleshooting

### 11.1 Common RDP Connection Issues

#### Issue 1: RDP Connection Refused
**Symptoms:**
- "Remote Desktop can't connect to the remote computer"
- Connection timeout

**Solutions:**
```bash
# Check if XRDP service is running
sudo systemctl status xrdp

# If not running, start it
sudo systemctl start xrdp
sudo systemctl enable xrdp

# Check if port 3389 is listening
sudo netstat -tlnp | grep :3389

# Check firewall (Ubuntu firewall)
sudo ufw status
sudo ufw allow 3389/tcp

# Restart XRDP service
sudo systemctl restart xrdp
```

#### Issue 2: Black Screen After Login
**Symptoms:**
- RDP connects but shows only black screen
- Desktop environment doesn't load

**Solutions:**
```bash
# Check .xsession file
cat /home/<USER>/.xsession
# Should contain: startlxde

# If missing, recreate it
echo "startlxde" > /home/<USER>/.xsession
chmod +x /home/<USER>/.xsession

# Check LXDE installation
dpkg -l | grep lxde

# Reinstall LXDE if needed
sudo apt install --reinstall lxde-core

# Check XRDP logs
sudo tail -50 /var/log/xrdp-sesman.log
```

#### Issue 3: Authentication Failed
**Symptoms:**
- "Your credentials did not work"
- Login prompt keeps appearing

**Solutions:**
```bash
# Reset ubuntu user password
sudo passwd ubuntu

# Check user account status
sudo passwd -S ubuntu

# Verify user is not locked
sudo usermod -U ubuntu

# Check XRDP authentication logs
sudo tail -50 /var/log/xrdp.log | grep -i auth
```

#### Issue 4: Terminal Not Opening
**Symptoms:**
- Terminal icon doesn't respond
- No terminal emulator available

**Solutions:**
```bash
# Install multiple terminal options
sudo apt install -y lxterminal xterm gnome-terminal

# Test terminals individually
lxterminal &
xterm &
gnome-terminal &

# Set default terminal
sudo update-alternatives --install /usr/bin/x-terminal-emulator x-terminal-emulator /usr/bin/lxterminal 50

# Check desktop shortcuts
ls -la /home/<USER>/Desktop/Terminal.desktop
chmod +x /home/<USER>/Desktop/Terminal.desktop
```

#### Issue 5: VS Code Won't Start
**Symptoms:**
- VS Code icon doesn't respond
- Application fails to launch

**Solutions:**
```bash
# Check VS Code installation
code --version

# Install missing dependencies
sudo apt install -y libnss3 libatk-bridge2.0-0 libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2

# Start with verbose logging
code --verbose

# Check for permission issues
sudo chown -R ubuntu:ubuntu /home/<USER>/.config/Code

# Try starting from terminal
code --no-sandbox
```

### 11.2 Performance Troubleshooting

#### Issue 1: Slow RDP Performance
**Symptoms:**
- Laggy mouse/keyboard response
- Slow screen updates

**Solutions:**
```bash
# Check system resources
htop

# Monitor network latency
ping -c 10 *******

# Optimize RDP client settings (Windows):
# - Set color depth to 16-bit
# - Disable visual effects
# - Use LAN connection speed setting

# Check instance type
curl -s http://***************/latest/meta-data/instance-type

# Consider upgrading to c5.xlarge or m5.xlarge
```

#### Issue 2: High CPU Usage
**Symptoms:**
- System becomes unresponsive
- Applications run slowly

**Solutions:**
```bash
# Identify CPU-intensive processes
top -o %CPU

# Kill unnecessary processes
sudo pkill -f firefox
sudo pkill -f chrome

# Disable unnecessary services
sudo systemctl disable snapd
sudo systemctl stop snapd

# Check for runaway processes
ps aux --sort=-%cpu | head -10
```

### 11.3 Log File Analysis

#### Key Log Files for Troubleshooting:
```bash
# XRDP main log
sudo tail -f /var/log/xrdp.log

# XRDP session manager log
sudo tail -f /var/log/xrdp-sesman.log

# System log
sudo journalctl -f -u xrdp

# Authentication log
sudo tail -f /var/log/auth.log | grep xrdp

# Desktop session log
tail -f ~/.xsession-errors
```

#### Common Error Messages:

**Error 1:** `Window manager (pid XXXX, display 10) exited with non-zero exit code 127`
```bash
# Solution: Desktop environment not found
sudo apt install --reinstall lxde-core
echo "startlxde" > /home/<USER>/.xsession
```

**Error 2:** `Failed to start session`
```bash
# Solution: Permission or configuration issue
sudo chown ubuntu:ubuntu /home/<USER>/.xsession
chmod +x /home/<USER>/.xsession
sudo systemctl restart xrdp
```

**Error 3:** `Connection refused`
```bash
# Solution: Service or firewall issue
sudo systemctl start xrdp
sudo ufw allow 3389/tcp
```

### 11.4 Emergency Recovery Procedures

#### Complete LXDE Reinstallation:
```bash
# Remove existing configuration
rm -rf /home/<USER>/.config/lx*
rm -rf /home/<USER>/.config/openbox

# Reinstall LXDE
sudo apt remove --purge lxde-*
sudo apt autoremove
sudo apt install -y lxde-core lxde-common lxterminal pcmanfm

# Reconfigure session
echo "startlxde" > /home/<USER>/.xsession
chmod +x /home/<USER>/.xsession

# Restart services
sudo systemctl restart xrdp
```

#### XRDP Complete Reset:
```bash
# Stop XRDP
sudo systemctl stop xrdp

# Remove configuration
sudo rm -rf /etc/xrdp/xrdp.ini
sudo rm -rf /etc/xrdp/sesman.ini

# Reinstall XRDP
sudo apt remove --purge xrdp
sudo apt install -y xrdp

# Apply optimized configuration (repeat from Section 5)
# Restart service
sudo systemctl start xrdp
```

---

## 12. Resetting RDP Password for Ubuntu User

If you've forgotten the RDP password that was set for the `ubuntu` user during initial configuration, you can reset it using SSH access. This section provides multiple methods to reset the password depending on your access situation.

### Method 1: Reset Password via SSH (Recommended)

This is the most straightforward method if you still have SSH access to your EC2 instance.

#### Step 8.1: Connect via SSH

Connect to your EC2 instance using your private key:

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

#### Step 8.2: Reset the Ubuntu User Password

Once connected via SSH, reset the password for the `ubuntu` user:

```bash
sudo passwd ubuntu
```

You will be prompted to:
1. Enter a new password
2. Confirm the new password

**Example output:**
```
New password: [enter your new password]
Retype new password: [confirm your new password]
passwd: password updated successfully
```

#### Step 8.3: Verify RDP Service Status

Ensure the RDP service is running properly:

```bash
sudo systemctl status xrdp
```

If it's not running, start it:

```bash
sudo systemctl start xrdp
sudo systemctl enable xrdp
```

#### Step 8.4: Test RDP Connection

1. Open **Remote Desktop Connection** on Windows (`mstsc`)
2. Enter your EC2 instance's public IP
3. Use the new credentials:
   - **Username:** `ubuntu`
   - **Password:** Your newly set password

### Method 2: Reset Password via AWS Systems Manager (Alternative)

If you have AWS Systems Manager Session Manager enabled, you can use it as an alternative to SSH.

#### Step 8.1: Enable Session Manager (if not already enabled)

1. In AWS Console, go to **EC2** → **Instances**
2. Select your instance
3. Ensure it has an IAM role with `AmazonSSMManagedInstanceCore` policy
4. If not, create and attach the role:

```bash
# Create IAM role (via AWS CLI)
aws iam create-role --role-name EC2-SSM-Role --assume-role-policy-document '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}'

# Attach policy
aws iam attach-role-policy --role-name EC2-SSM-Role --policy-arn arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore

# Create instance profile
aws iam create-instance-profile --instance-profile-name EC2-SSM-Profile
aws iam add-role-to-instance-profile --instance-profile-name EC2-SSM-Profile --role-name EC2-SSM-Role
```

#### Step 8.2: Connect via Session Manager

1. In AWS Console, go to **Systems Manager** → **Session Manager**
2. Click **Start session**
3. Select your EC2 instance
4. Click **Start session**

#### Step 8.3: Reset Password

In the Session Manager terminal:

```bash
sudo passwd ubuntu
```

### Method 3: Reset Password via EC2 User Data (Advanced)

If you don't have SSH access and Session Manager isn't available, you can use EC2 User Data to reset the password on next boot.

#### Step 8.1: Stop the Instance

1. In AWS Console, go to **EC2** → **Instances**
2. Select your instance
3. Click **Instance state** → **Stop instance**

#### Step 8.2: Modify User Data

1. Right-click the stopped instance → **Instance settings** → **Edit user data**
2. Add the following script:

```bash
#!/bin/bash
# Reset ubuntu user password
echo 'ubuntu:YourNewPassword123!' | chpasswd
# Remove this user data after execution (optional)
# echo "" > /var/lib/cloud/instance/user-data.txt
```

**⚠️ Security Note:** Replace `YourNewPassword123!` with your desired password. Be aware that user data is visible in the EC2 console.

#### Step 8.3: Start the Instance

1. Click **Instance state** → **Start instance**
2. Wait for the instance to boot completely
3. The password will be reset during the boot process

#### Step 8.4: Clear User Data (Security Best Practice)

After the password is reset:
1. Stop the instance again
2. Edit user data and clear the content
3. Start the instance

### Method 4: Create New User with RDP Access (Alternative Solution)

If you prefer to create a new user instead of resetting the existing password:

#### Step 4.1: Connect via SSH

```bash
ssh -i "path/to/your-key.pem" ubuntu@your-ec2-public-ip
```

#### Step 4.2: Create New User

```bash
# Create new user
sudo adduser newrdpuser

# Add user to sudo group (optional)
sudo usermod -aG sudo newrdpuser

# Set up RDP session for new user
echo "xfce4-session" | sudo tee /home/<USER>/.xsession
sudo chown newrdpuser:newrdpuser /home/<USER>/.xsession
```

#### Step 4.3: Test New User RDP Access

Use the new username and password for RDP connection.

### Password Security Best Practices

#### Strong Password Requirements
- **Minimum 12 characters**
- **Mix of uppercase, lowercase, numbers, and symbols**
- **Avoid common words or patterns**
- **Example format:** `MySecure2024!RDP`

#### Additional Security Measures

1. **Enable SSH Key Authentication Only** (disable password auth for SSH):
   ```bash
   sudo nano /etc/ssh/sshd_config
   # Set: PasswordAuthentication no
   sudo systemctl restart ssh
   ```

2. **Restrict RDP Access by IP**:
   - In AWS Security Group, limit RDP (port 3389) to specific IP addresses
   - Avoid using `0.0.0.0/0` (anywhere)

3. **Enable AWS CloudTrail** for audit logging:
   ```bash
   # Monitor RDP login attempts in CloudWatch logs
   sudo tail -f /var/log/xrdp.log
   ```

4. **Regular Password Rotation**:
   - Change RDP password every 90 days
   - Document password changes in your security log

### Troubleshooting Password Reset Issues

#### Issue 1: "Authentication failure" after password reset

**Solution:**
```bash
# Restart xrdp service
sudo systemctl restart xrdp

# Check service status
sudo systemctl status xrdp

# Verify user exists and has password set
sudo getent passwd ubuntu
```

#### Issue 2: User data script didn't execute

**Solution:**
```bash
# Check cloud-init logs
sudo cat /var/log/cloud-init-output.log
sudo cat /var/log/cloud-init.log

# Manually run user data script
sudo bash /var/lib/cloud/instance/user-data.txt
```

#### Issue 3: Session Manager not working

**Solution:**
```bash
# Install SSM agent (if missing)
sudo snap install amazon-ssm-agent --classic
sudo systemctl start snap.amazon-ssm-agent.amazon-ssm-agent.service
sudo systemctl enable snap.amazon-ssm-agent.amazon-ssm-agent.service
```

### Verification Checklist

After resetting the password, verify the following:

- [ ] RDP service is running: `sudo systemctl status xrdp`
- [ ] User password is set: `sudo passwd -S ubuntu`
- [ ] Security group allows RDP (port 3389) from your IP
- [ ] EC2 instance is running and accessible
- [ ] XFCE desktop environment is installed
- [ ] `.xsession` file exists in user's home directory
- [ ] Can connect via RDP with new credentials

## 9. RDP Performance Optimization

If you're experiencing slow RDP performance, especially with cross-region connections (like US-East-1 from India), this section provides comprehensive optimization strategies.

### 9.1 Performance Issues Analysis

#### Common Symptoms:
- **Slow screen updates** and laggy mouse/keyboard response
- **High latency** during remote sessions
- **Choppy video/animation** rendering
- **Frequent disconnections** or timeouts
- **Poor audio quality** (if enabled)

#### Root Causes:
1. **Geographic Distance**: US-East-1 to India (~200-300ms latency)
2. **Heavy Desktop Environment**: XFCE with full features enabled
3. **Unoptimized RDP Settings**: Default XRDP configuration
4. **Network Bandwidth**: Limited or variable internet connection
5. **EC2 Instance Resources**: CPU/Memory constraints during GUI operations

### 9.2 Hardware and Network Optimization

#### Step 9.2.1: Upgrade EC2 Instance Type

Your current `t3a.large` may be insufficient for smooth RDP performance. Consider upgrading:

**Recommended Instance Types:**
```bash
# Current: t3a.large (2 vCPU, 8 GB RAM)
# Better options:
# t3a.xlarge   (4 vCPU, 16 GB RAM) - Good for general use
# c5.xlarge    (4 vCPU, 8 GB RAM)  - CPU optimized
# m5.xlarge    (4 vCPU, 16 GB RAM) - Balanced performance
# c5.2xlarge   (8 vCPU, 16 GB RAM) - High performance
```

**To change instance type:**
1. Stop the EC2 instance
2. Right-click → **Instance Settings** → **Change Instance Type**
3. Select `c5.xlarge` or `m5.xlarge` for better performance
4. Start the instance

#### Step 9.2.2: Regional Optimization

**Option A: Move to Closer Region (Recommended)**
```bash
# Consider these regions for better latency from India:
# ap-south-1     (Mumbai)     - ~10-50ms latency
# ap-southeast-1 (Singapore)  - ~50-100ms latency
# ap-southeast-2 (Sydney)     - ~100-150ms latency
```

**Option B: Use AWS Global Accelerator**
- Improves performance by routing through AWS backbone
- Can reduce latency by 20-60%
- Additional cost but significant performance improvement

#### Step 9.2.3: Network Optimization

**Enable Enhanced Networking:**
```bash
# Check if enhanced networking is enabled
aws ec2 describe-instances --instance-ids i-1234567890abcdef0 --query 'Reservations[].Instances[].EnaSupport'

# Enable enhanced networking (requires instance stop)
aws ec2 modify-instance-attribute --instance-id i-1234567890abcdef0 --ena-support
```

### 9.3 XRDP Configuration Optimization

#### Step 9.3.1: Optimize XRDP Settings

Create optimized XRDP configuration:

```bash
# Backup original configuration
sudo cp /etc/xrdp/xrdp.ini /etc/xrdp/xrdp.ini.backup

# Apply performance optimizations
sudo tee /etc/xrdp/xrdp.ini > /dev/null << 'EOF'
[Globals]
ini_version=1
fork=true
port=3389
tcp_nodelay=true
tcp_keepalive=true
security_layer=rdp
crypt_level=high
certificate=
key_file=
ssl_protocols=TLSv1.2, TLSv1.3
autorun=
allow_channels=true
allow_multimon=true
bitmap_cache=true
bitmap_compression=true
bulk_compression=true
max_bpp=24
new_cursors=true
use_fastpath=both
require_credentials=true
login_retry=4
killloggedon=false
rdp_keepalive=true

[Xorg]
name=Xorg
lib=libxup.so
username=ask
password=ask
ip=127.0.0.1
port=-1
code=20
EOF
```

#### Step 9.3.2: Optimize Session Manager

```bash
# Edit session manager configuration
sudo nano /etc/xrdp/sesman.ini

# Add these optimizations to [Xorg] section:
# param1=-bs
# param2=-ac
# param3=-nolisten
# param4=tcp
# param5=-dpi
# param6=96
```

Or apply automatically:
```bash
sudo tee -a /etc/xrdp/sesman.ini > /dev/null << 'EOF'

[Xorg]
param1=-bs
param2=-ac
param3=-nolisten
param4=tcp
param5=-dpi
param6=96
EOF
```

### 9.4 Desktop Environment Optimization

#### Step 9.4.1: Switch to Lightweight Desktop (Recommended)

Replace XFCE with an even lighter desktop environment:

**Option A: LXDE (Lightest)**
```bash
# Remove XFCE (optional)
sudo apt remove --purge xfce4* -y
sudo apt autoremove -y

# Install LXDE
sudo apt update
sudo apt install -y lxde-core lxde-common

# Configure for RDP
echo "startlxde" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession
```

**Option B: LXQt (Modern and Light)**
```bash
# Install LXQt
sudo apt install -y lxqt-core

# Configure for RDP
echo "startlxqt" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession
```

**Option C: Optimize Existing XFCE**
```bash
# Disable unnecessary XFCE components
xfconf-query -c xfwm4 -p /general/use_compositing -s false
xfconf-query -c xfwm4 -p /general/frame_opacity -s 100
xfconf-query -c xfwm4 -p /general/inactive_opacity -s 100

# Disable desktop effects
xfconf-query -c xfwm4 -p /general/show_frame_shadow -s false
xfconf-query -c xfwm4 -p /general/show_popup_shadow -s false

# Set simple theme
xfconf-query -c xsettings -p /Net/ThemeName -s "Adwaita"
xfconf-query -c xfwm4 -p /general/theme -s "Default"
```

#### Step 9.4.2: Disable Resource-Heavy Services

```bash
# Disable unnecessary services
sudo systemctl disable bluetooth
sudo systemctl disable cups
sudo systemctl disable avahi-daemon
sudo systemctl disable ModemManager

# Stop services immediately
sudo systemctl stop bluetooth
sudo systemctl stop cups
sudo systemctl stop avahi-daemon
sudo systemctl stop ModemManager

# Remove unnecessary packages
sudo apt remove --purge thunderbird libreoffice-* -y
sudo apt autoremove -y
```

#### Step 9.4.3: Optimize Display Settings

```bash
# Create display optimization script
cat > /home/<USER>/optimize-display.sh << 'EOF'
#!/bin/bash
# Reduce color depth for better performance
xrandr --output default --depth 16

# Disable screen saver and power management
xset s off
xset -dpms
xset s noblank

# Optimize font rendering
echo "Xft.antialias: 0" >> ~/.Xresources
echo "Xft.hinting: 0" >> ~/.Xresources
xrdb ~/.Xresources
EOF

chmod +x /home/<USER>/optimize-display.sh

# Auto-run on session start
echo "/home/<USER>/optimize-display.sh" >> /home/<USER>/.xsession
```

### 9.5 Client-Side RDP Optimization

#### Step 9.5.1: Windows RDP Client Settings

**Optimize Remote Desktop Connection:**

1. **Open Remote Desktop Connection** (`mstsc`)
2. **Click "Show Options"**
3. **Display Tab:**
   - Set **Color depth** to **High Color (16 bit)**
   - **Uncheck** "Display the connection bar when I use the full screen"

4. **Local Resources Tab:**
   - **Uncheck** "Printers" and "Clipboard" if not needed
   - **Audio:** Set to "Do not play"

5. **Experience Tab:**
   - **Connection speed:** Select "LAN (10 Mbps or higher)"
   - **Uncheck all visual effects:**
     - Desktop background
     - Font smoothing
     - Desktop composition
     - Show contents of windows while dragging
     - Menu and window animation
     - Themes

6. **Advanced Tab:**
   - **Server authentication:** "Connect and don't warn me"

#### Step 9.5.2: Alternative RDP Clients

**Consider using optimized RDP clients:**

**FreeRDP (Command Line):**
```bash
# Install on local Windows (via WSL) or Linux
sudo apt install freerdp2-x11

# Connect with optimized settings
xfreerdp /v:your-ec2-ip /u:ubuntu /p:yourpassword /bpp:16 /compression /fast-path /gfx-progressive /rfx
```

**RDP Wrapper (Windows):**
- Download and install RDP Wrapper
- Provides better compression and performance options

### 9.6 System Performance Monitoring

#### Step 9.6.1: Monitor Resource Usage

```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Create monitoring script
cat > /home/<USER>/monitor-performance.sh << 'EOF'
#!/bin/bash
echo "=== CPU Usage ==="
top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}'

echo "=== Memory Usage ==="
free -h | awk 'NR==2{printf "Memory Usage: %s/%s (%.2f%%)\n", $3,$2,$3*100/$2 }'

echo "=== Disk Usage ==="
df -h | awk '$NF=="/"{printf "Disk Usage: %d/%dGB (%s)\n", $3,$2,$5}'

echo "=== Network Connections ==="
ss -tuln | grep :3389
EOF

chmod +x /home/<USER>/monitor-performance.sh
```

#### Step 9.6.2: Performance Testing

```bash
# Test network latency to your location
ping -c 10 *******

# Test bandwidth
sudo apt install speedtest-cli
speedtest-cli

# Monitor RDP connections
sudo tail -f /var/log/xrdp.log
```

### 9.7 Advanced Optimization Techniques

#### Step 9.7.1: Kernel Parameter Tuning

```bash
# Optimize network parameters
sudo tee -a /etc/sysctl.conf > /dev/null << 'EOF'
# Network optimizations for RDP
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
EOF

# Apply changes
sudo sysctl -p
```

#### Step 9.7.2: GPU Acceleration (if available)

```bash
# Check for GPU
lspci | grep -i vga

# Install GPU drivers if available
sudo apt install -y mesa-utils

# Enable GPU acceleration for X11
sudo nano /etc/X11/xorg.conf.d/20-intel.conf
# Add:
# Section "Device"
#    Identifier "Intel Graphics"
#    Driver "intel"
#    Option "AccelMethod" "sna"
#    Option "TearFree" "true"
# EndSection
```

### 9.8 Performance Benchmarking

#### Step 9.8.1: Before and After Testing

**Test RDP Performance:**
```bash
# Create performance test script
cat > /home/<USER>/rdp-performance-test.sh << 'EOF'
#!/bin/bash
echo "Starting RDP Performance Test..."

# Test 1: Window movement
echo "Test 1: Moving windows (should be smooth)"
for i in {1..10}; do
    xterm -geometry 80x24+$((i*50))+$((i*30)) -e "sleep 2" &
done
sleep 5
killall xterm

# Test 2: Text rendering
echo "Test 2: Text rendering speed"
time (for i in {1..100}; do echo "Performance test line $i"; done)

# Test 3: Graphics performance
echo "Test 3: Basic graphics test"
glxgears -info | head -5

echo "Performance test completed"
EOF

chmod +x /home/<USER>/rdp-performance-test.sh
```

### 9.9 Troubleshooting Performance Issues

#### Common Issues and Solutions:

**Issue 1: High CPU Usage**
```bash
# Check processes
htop
# Kill unnecessary processes
sudo pkill -f firefox
sudo pkill -f chrome
```

**Issue 2: Memory Exhaustion**
```bash
# Check memory usage
free -h
# Clear cache
sudo sync && echo 3 | sudo tee /proc/sys/vm/drop_caches
```

**Issue 3: Network Latency**
```bash
# Test latency to your location
mtr -r -c 10 your-local-ip
# Consider changing AWS region
```

### 9.10 Restart Services and Apply Changes

After making all optimizations:

```bash
# Restart all relevant services
sudo systemctl restart xrdp
sudo systemctl restart lightdm  # or gdm3

# Reboot for kernel parameter changes
sudo reboot
```

### 9.11 Performance Optimization Checklist

After implementing optimizations, verify:

- [ ] EC2 instance type upgraded (c5.xlarge or better)
- [ ] Enhanced networking enabled
- [ ] XRDP configuration optimized
- [ ] Lightweight desktop environment installed
- [ ] Unnecessary services disabled
- [ ] Client RDP settings optimized
- [ ] Network parameters tuned
- [ ] Performance monitoring tools installed
- [ ] Latency tested and acceptable (<150ms)

**Expected Performance Improvements:**
- **50-70% reduction** in screen update lag
- **30-50% improvement** in mouse/keyboard responsiveness
- **Reduced CPU usage** from 60-80% to 20-40%
- **Better stability** with fewer disconnections

## 10. Complete Migration to Mumbai Region with Optimized Setup

If you're experiencing terminal emulator issues and Ubuntu errors, the best solution is a fresh setup in Mumbai region with optimized configuration for your specific needs (VS Code + CLI terminals).

### 10.1 Current Environment Diagnosis

#### Step 10.1.1: Check Current Desktop Environment

First, let's identify what's currently installed:

```bash
# Check current desktop environment
echo $XDG_CURRENT_DESKTOP
echo $DESKTOP_SESSION

# Check installed desktop packages
dpkg -l | grep -E "(xfce|lxde|gnome|kde|mate)"

# Check running desktop processes
ps aux | grep -E "(xfce|lxde|gnome|kde)"

# Check display manager
systemctl status lightdm
systemctl status gdm3
systemctl status sddm
```

#### Step 10.1.2: Check Current Issues

```bash
# Check system errors
sudo journalctl -p 3 -xb

# Check RDP logs for errors
sudo tail -50 /var/log/xrdp.log
sudo tail -50 /var/log/xrdp-sesman.log

# Check if terminal emulator is installed
which gnome-terminal
which xfce4-terminal
which lxterminal
which xterm
```

### 10.2 Mumbai Region Migration Strategy

#### Step 10.2.1: Create AMI from Current Instance (Backup)

Before migration, create a backup:

```bash
# From AWS CLI or Console:
# 1. Stop current instance
# 2. Create AMI: "ubuntu-rdp-backup-YYYY-MM-DD"
# 3. Note AMI ID for potential rollback
```

#### Step 10.2.2: Launch New Instance in ap-south-1 (Mumbai)

**Recommended Configuration:**
- **Region**: ap-south-1 (Asia Pacific - Mumbai)
- **AMI**: Ubuntu Server 24.04 LTS
- **Instance Type**: `c5.xlarge` (4 vCPU, 8 GB RAM) - Optimal for VS Code + terminals
- **Storage**: 30 GB GP3 SSD (better performance than GP2)
- **Security Group**:
  - SSH (22) from your IP
  - RDP (3389) from your IP

**Launch Command (AWS CLI):**
```bash
aws ec2 run-instances \
    --region ap-south-1 \
    --image-id ami-0f58b397bc5c1f2e8 \
    --instance-type c5.xlarge \
    --key-name your-key-pair \
    --security-group-ids sg-xxxxxxxxx \
    --block-device-mappings '[{"DeviceName":"/dev/sda1","Ebs":{"VolumeSize":30,"VolumeType":"gp3"}}]' \
    --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=Ubuntu-RDP-Mumbai}]'
```

### 10.3 Optimized LXDE Setup for VS Code Development

#### Step 10.3.1: Connect and Update System

```bash
# Connect to new Mumbai instance
ssh -i "your-key.pem" ubuntu@new-mumbai-ip

# Update system
sudo apt update && sudo apt upgrade -y
```

#### Step 10.3.2: Install Minimal LXDE Desktop

```bash
# Install only essential LXDE components
sudo apt install -y \
    lxde-core \
    lxde-common \
    lxterminal \
    pcmanfm \
    lxpanel \
    openbox \
    obconf \
    lxappearance

# Install essential utilities
sudo apt install -y \
    firefox \
    gedit \
    file-manager \
    gvfs \
    gvfs-backends
```

#### Step 10.3.3: Install and Configure XRDP

```bash
# Install XRDP
sudo apt install -y xrdp

# Create optimized XRDP configuration for LXDE
sudo tee /etc/xrdp/xrdp.ini > /dev/null << 'EOF'
[Globals]
ini_version=1
fork=true
port=3389
tcp_nodelay=true
tcp_keepalive=true
security_layer=rdp
crypt_level=high
bitmap_cache=true
bitmap_compression=true
bulk_compression=true
max_bpp=24
new_cursors=true
use_fastpath=both
require_credentials=true
rdp_keepalive=true

[Xorg]
name=Xorg
lib=libxup.so
username=ask
password=ask
ip=127.0.0.1
port=-1
code=20
EOF

# Configure session for LXDE
echo "startlxde" | sudo tee /home/<USER>/.xsession
sudo chown ubuntu:ubuntu /home/<USER>/.xsession

# Set password for ubuntu user
sudo passwd ubuntu

# Enable and start XRDP
sudo systemctl enable xrdp
sudo systemctl start xrdp
```

#### Step 10.3.4: Optimize LXDE for Development

```bash
# Create LXDE optimization script
cat > /home/<USER>/optimize-lxde.sh << 'EOF'
#!/bin/bash

# Disable unnecessary LXDE components
mkdir -p ~/.config/autostart

# Disable screensaver
echo "[Desktop Entry]
Type=Application
Name=Disable Screensaver
Exec=xset s off -dpms
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true" > ~/.config/autostart/disable-screensaver.desktop

# Configure LXTerminal for development
mkdir -p ~/.config/lxterminal
cat > ~/.config/lxterminal/lxterminal.conf << 'TERM_EOF'
[general]
fontname=Monospace 11
selchars=-A-Za-z0-9,./?%&#:_
scrollback=1000
bgcolor=rgb(0,0,0)
fgcolor=rgb(255,255,255)
palette_color_0=rgb(0,0,0)
palette_color_1=rgb(178,24,24)
palette_color_2=rgb(24,178,24)
palette_color_3=rgb(178,104,24)
palette_color_4=rgb(24,24,178)
palette_color_5=rgb(178,24,178)
palette_color_6=rgb(24,178,178)
palette_color_7=rgb(178,178,178)
palette_color_8=rgb(104,104,104)
palette_color_9=rgb(255,84,84)
palette_color_10=rgb(84,255,84)
palette_color_11=rgb(255,255,84)
palette_color_12=rgb(84,84,255)
palette_color_13=rgb(255,84,255)
palette_color_14=rgb(84,255,255)
palette_color_15=rgb(255,255,255)
color_preset=Custom
disallowbold=false
cursorblinks=false
cursorunderline=false
audiblebell=false
tabpos=top
geometry_columns=80
geometry_rows=24
hidescrollbar=false
hidemenubar=false
hideclosebutton=false
hidepointer=false
disallowbold=false
TERM_EOF

# Configure PCManFM (file manager)
mkdir -p ~/.config/pcmanfm/LXDE
cat > ~/.config/pcmanfm/LXDE/pcmanfm.conf << 'FM_EOF'
[config]
bm_open_method=0
su_cmd=gksu %s
terminal=lxterminal
archiver=file-roller

[volume]
mount_on_startup=1
mount_removable=1
autorun=1

[ui]
always_show_tabs=0
max_tab_chars=32
win_width=640
win_height=480
splitter_pos=150
media_in_new_tab=0
desktop_folder_new_win=0
change_tab_on_drop=1
close_on_unmount=1
focus_previous=0
side_pane_mode=places
view_mode=icon_view
show_hidden=0
sort=name;ascending;
toolbar=newtab;navigation;home;
show_statusbar=1
pathbar_mode_buttons=0
FM_EOF

echo "LXDE optimization completed!"
EOF

chmod +x /home/<USER>/optimize-lxde.sh
./optimize-lxde.sh
```

### 10.4 Install VS Code for Development

#### Step 10.4.1: Install VS Code

```bash
# Install VS Code
sudo apt update
sudo apt install -y wget gpg software-properties-common

# Add Microsoft GPG key and repository
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'

# Install VS Code
sudo apt update
sudo apt install -y code

# Install essential development tools
sudo apt install -y \
    git \
    curl \
    wget \
    build-essential \
    python3 \
    python3-pip \
    nodejs \
    npm \
    default-jdk
```

#### Step 10.4.2: Configure VS Code for RDP

```bash
# Create VS Code settings for optimal RDP performance
mkdir -p ~/.config/Code/User

cat > ~/.config/Code/User/settings.json << 'EOF'
{
    "window.titleBarStyle": "custom",
    "editor.fontFamily": "monospace",
    "editor.fontSize": 14,
    "editor.renderWhitespace": "none",
    "editor.minimap.enabled": false,
    "workbench.activityBar.visible": true,
    "workbench.statusBar.visible": true,
    "workbench.sideBar.location": "left",
    "workbench.editor.enablePreview": false,
    "workbench.startupEditor": "newUntitledFile",
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "terminal.integrated.fontFamily": "monospace",
    "terminal.integrated.fontSize": 12,
    "workbench.colorTheme": "Default Dark+",
    "editor.smoothScrolling": false,
    "workbench.list.smoothScrolling": false,
    "editor.cursorBlinking": "solid",
    "editor.cursorSmoothCaretAnimation": false,
    "workbench.reduceMotion": "on",
    "window.dialogStyle": "custom",
    "workbench.tree.renderIndentGuides": "none"
}
EOF
```

### 10.5 Create Development Environment Setup

#### Step 10.5.1: Create Desktop Shortcuts

```bash
# Create desktop directory
mkdir -p ~/Desktop

# Create VS Code shortcut
cat > ~/Desktop/VSCode.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Visual Studio Code
Comment=Code Editing. Redefined.
Exec=/usr/bin/code
Icon=code
Terminal=false
Categories=Development;IDE;
MimeType=text/plain;inode/directory;
EOF

# Create Terminal shortcut
cat > ~/Desktop/Terminal.desktop << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=Terminal
Comment=Terminal Emulator
Exec=lxterminal
Icon=terminal
Terminal=false
Categories=System;TerminalEmulator;
EOF

# Make shortcuts executable
chmod +x ~/Desktop/*.desktop
```

#### Step 10.5.2: Create Quick Launch Panel

```bash
# Configure LXDE panel for quick access
mkdir -p ~/.config/lxpanel/LXDE/panels

cat > ~/.config/lxpanel/LXDE/panels/panel << 'EOF'
# lxpanel <profile> config file. Manually editing is not recommended.
# Use preference dialog in lxpanel to adjust config when you can.

Global {
    edge=bottom
    allign=left
    margin=0
    widthtype=percent
    width=100
    height=26
    transparent=0
    tintcolor=#000000
    alpha=0
    setdocktype=1
    setpartialstrut=1
    autohide=0
    heightwhenhidden=2
    usefontcolor=1
    fontcolor=#ffffff
    background=1
    backgroundfile=/usr/share/lxpanel/images/background.png
}

Plugin {
    type=menu
    Config {
        image=/usr/share/pixmaps/lubuntu-logo.png
        system {
        }
        separator {
        }
        item {
            command=run
        }
        separator {
        }
        item {
            image=gnome-logout
            command=logout
        }
    }
}

Plugin {
    type=launchbar
    Config {
        Button {
            id=lxterminal.desktop
        }
        Button {
            id=code.desktop
        }
        Button {
            id=pcmanfm.desktop
        }
        Button {
            id=firefox.desktop
        }
    }
}

Plugin {
    type=taskbar
    expand=1
    Config {
        tooltips=1
        IconsOnly=0
        ShowAllDesks=0
        UseMouseWheel=1
        UseUrgencyHint=1
        FlatButton=0
        MaxTaskWidth=150
        spacing=1
    }
}

Plugin {
    type=tray
}

Plugin {
    type=dclock
    Config {
        ClockFmt=%R
        TooltipFmt=%A %x
        BoldFont=0
        IconOnly=0
        CenterText=0
    }
}
EOF
```

### 10.6 Performance Testing and Validation

#### Step 10.6.1: Test RDP Connection

```bash
# Test from your local machine
# 1. Open Remote Desktop Connection
# 2. Connect to Mumbai instance IP
# 3. Username: ubuntu
# 4. Password: [your set password]

# Expected latency from India to Mumbai: 10-50ms
ping -c 10 your-mumbai-instance-ip
```

#### Step 10.6.2: Validate Environment

```bash
# Create validation script
cat > /home/<USER>/validate-setup.sh << 'EOF'
#!/bin/bash

echo "=== Environment Validation ==="

echo "1. Desktop Environment:"
echo "   Current: $XDG_CURRENT_DESKTOP"
echo "   Session: $DESKTOP_SESSION"

echo "2. Available Applications:"
echo "   VS Code: $(which code)"
echo "   Terminal: $(which lxterminal)"
echo "   File Manager: $(which pcmanfm)"

echo "3. System Resources:"
echo "   CPU: $(nproc) cores"
echo "   Memory: $(free -h | awk 'NR==2{print $2}')"
echo "   Disk: $(df -h / | awk 'NR==2{print $4}') available"

echo "4. Network Performance:"
echo "   Instance Region: $(curl -s http://***************/latest/meta-data/placement/region)"
echo "   Instance Type: $(curl -s http://***************/latest/meta-data/instance-type)"

echo "5. RDP Service Status:"
systemctl is-active xrdp

echo "=== Validation Complete ==="
EOF

chmod +x /home/<USER>/validate-setup.sh
./validate-setup.sh
```

### 10.7 Troubleshooting Common Issues

#### Issue 1: Terminal Not Opening in RDP

```bash
# Install alternative terminals
sudo apt install -y xterm gnome-terminal

# Test terminals
xterm &
lxterminal &
gnome-terminal &

# Set default terminal
sudo update-alternatives --install /usr/bin/x-terminal-emulator x-terminal-emulator /usr/bin/lxterminal 50
```

#### Issue 2: VS Code Not Starting

```bash
# Check VS Code installation
code --version

# Start with verbose logging
code --verbose

# Check for missing dependencies
sudo apt install -y libnss3 libatk-bridge2.0-0 libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
```

#### Issue 3: Desktop Environment Errors

```bash
# Reset LXDE configuration
rm -rf ~/.config/lxsession
rm -rf ~/.config/openbox
rm -rf ~/.config/lxpanel

# Restart session
sudo systemctl restart xrdp
```

### 10.8 Migration Checklist

**Pre-Migration:**
- [ ] Create AMI backup of current instance
- [ ] Document current applications and data
- [ ] Test Mumbai region connectivity

**Migration:**
- [ ] Launch c5.xlarge in ap-south-1 (Mumbai)
- [ ] Install minimal LXDE desktop
- [ ] Configure optimized XRDP
- [ ] Install VS Code with RDP optimizations
- [ ] Create desktop shortcuts and panel

**Post-Migration:**
- [ ] Test RDP connection (should be <50ms latency)
- [ ] Verify VS Code functionality
- [ ] Test terminal emulator (lxterminal)
- [ ] Validate all shortcuts work
- [ ] Monitor resource usage (<40% CPU)

**Expected Results:**
- **Latency**: 10-50ms (vs 200-300ms from US-East-1)
- **Performance**: 70-80% improvement in responsiveness
- **Stability**: No more Ubuntu errors or terminal issues
- **Resource Usage**: Optimized for VS Code + 2 terminals

This setup will give you a clean, optimized environment specifically tailored for your development needs with VS Code and terminal access.

## 11. (Optional) Installing Additional Development Tools

You can install VS Code directly on the remote desktop.

```bash
# Via RDP Terminal or SSH
sudo apt update
sudo apt install -y wget gpg

# Add Microsoft GPG key
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/

# Add VS Code repository
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'

# Install VS Code
sudo apt update
sudo apt install -y code
```
