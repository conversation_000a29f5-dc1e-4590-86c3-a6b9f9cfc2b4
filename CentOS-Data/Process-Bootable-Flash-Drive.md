# Creating a Bootable CentOS 7 Flash Drive on Windows 10

## Executive Summary

This document provides a complete step-by-step guide to create a bootable CentOS 7 flash drive on Windows 10 using Rufus software. This bootable drive will be used for troubleshooting and recovery operations on CentOS servers with boot failures.

---

## 1. Recommended ISO Selection

From the available ISO files on https://buildlogs.centos.org/centos/7/isos/x86_64/, here are the recommendations:

### **Primary Recommendation: CentOS-7-livecd-x86_64.iso**
- **File:** `CentOS-7-livecd-x86_64.iso`
- **Size:** 686M
- **Date:** 2014-07-04 14:41
- **Why this choice:**
  - Smallest size for faster download
  - Contains essential recovery tools
  - No desktop environment overhead
  - Perfect for server troubleshooting
  - Compatible with both BIOS and UEFI systems

### **Alternative Options:**

#### For GUI-based Recovery:
- **CentOS-7-live-GNOME-x86_64.iso** (1.0G) - If you prefer a graphical interface
- **CentOS-7-live-KDE-x86_64.iso** (1.2G) - KDE desktop environment

#### For Full Installation Capability:
- **CentOS-7-x86_64-DVD-1609-99.iso** (4.0G) - Complete installation media
- **CentOS-7-x86_64-Minimal-1609-99.iso** (608M) - Minimal installation

---

## 2. Prerequisites

### **Hardware Requirements:**
- USB flash drive (minimum 8GB recommended, 4GB minimum for livecd)
- Windows 10 computer with internet access
- Target servers with USB boot capability

### **Software Requirements:**
- Rufus (free USB creation tool)
- Web browser for downloading

---

## 3. Step-by-Step Process

### **Step 3.1: Download Required Files**

#### **Download CentOS 7 ISO:**
1. Open your web browser
2. Navigate to: https://buildlogs.centos.org/centos/7/isos/x86_64/
3. Right-click on `CentOS-7-livecd-x86_64.iso`
4. Select "Save link as..." or "Save target as..."
5. Choose a location (e.g., `C:\Downloads\`)
6. Wait for download to complete (686MB)

#### **Download SHA256 Checksum (Optional but Recommended):**
1. Download `sha256sum.txt` from the same directory
2. This file contains checksums to verify ISO integrity

#### **Download Rufus:**
1. Go to: https://rufus.ie/
2. Click "Download" for the latest version
3. Choose "Rufus Portable" (no installation required)
4. Save to `C:\Downloads\` or preferred location

### **Step 3.2: Verify ISO Integrity (Recommended)**

#### **Using Windows PowerShell:**
1. Press `Win + X` and select "Windows PowerShell"
2. Navigate to your download directory:
   ```powershell
   cd C:\Downloads
   ```
3. Calculate the SHA256 hash:
   ```powershell
   Get-FileHash -Algorithm SHA256 CentOS-7-livecd-x86_64.iso
   ```
4. Compare the output with the hash in `sha256sum.txt`
5. If hashes match, the file is intact

### **Step 3.3: Prepare USB Flash Drive**

#### **Important Warning:**
⚠️ **All data on the USB drive will be erased. Backup any important files first.**

#### **Insert and Identify USB Drive:**
1. Insert your USB flash drive into the computer
2. Open "This PC" or "File Explorer"
3. Note the drive letter assigned (e.g., E:, F:, G:)
4. Ensure the drive has at least 1GB free space

### **Step 3.4: Create Bootable USB with Rufus**

#### **Launch Rufus:**
1. Navigate to your Rufus download location
2. Double-click `rufus-x.x.exe` (where x.x is the version)
3. If prompted by Windows Defender, click "Allow"

#### **Configure Rufus Settings:**

**Device Selection:**
1. In the "Device" dropdown, select your USB drive
2. Verify it's the correct drive by checking the size

**Boot Selection:**
1. Click "SELECT" button next to "Boot selection"
2. Navigate to your CentOS ISO file
3. Select `CentOS-7-livecd-x86_64.iso`
4. Click "Open"

**Partition Scheme and Target System:**
- **For BIOS systems:** Select "MBR" partition scheme
- **For UEFI systems:** Select "GPT" partition scheme
- **For maximum compatibility:** Select "MBR" (works with both BIOS and UEFI in CSM mode)

**File System:**
- Select "FAT32" (recommended for compatibility)

**Cluster Size:**
- Leave as default (usually 4096 bytes)

**Volume Label:**
- Enter "CentOS7-Live" or leave default

#### **Advanced Options (Click "Show advanced drive properties"):**
- ✅ Check "Check device for bad blocks" (optional, takes longer)
- ✅ Check "Create extended label and icon files"

### **Step 3.5: Create the Bootable Drive**

1. Review all settings one final time
2. Click "START"
3. **Warning Dialog:** Rufus will warn that all data will be destroyed
4. Click "OK" to confirm
5. **ISO Image Mode Dialog:** Select "Write in ISO Image mode (Recommended)"
6. Click "OK"
7. Wait for the process to complete (5-15 minutes depending on USB speed)
8. When complete, you'll see "READY" in the status bar

### **Step 3.6: Verify Bootable USB Creation**

1. Open "This PC" and navigate to your USB drive
2. You should see files like:
   - `isolinux/` directory
   - `LiveOS/` directory
   - `EFI/` directory (if GPT was selected)
   - Various other files and directories

---

## 4. Testing the Bootable USB

### **Test on a Non-Critical System:**
1. Insert the USB into a test computer
2. Restart the computer
3. Enter BIOS/UEFI setup (usually F2, F12, DEL, or ESC during startup)
4. Set USB as first boot device
5. Save and exit
6. The system should boot to CentOS 7 Live environment

### **Expected Boot Sequence:**
1. Initial boot menu appears
2. Select "Start CentOS Linux 7"
3. System loads into live environment
4. You should see a command prompt or desktop

---

## 5. Using the Bootable USB for Server Recovery

### **BIOS Configuration for Target Servers:**
1. Insert USB into the problematic server
2. Power on and enter BIOS/UEFI setup
3. **Critical Settings:**
   - Disable Secure Boot
   - Enable Legacy Boot/CSM mode
   - Set USB as first boot device
4. Save and exit

### **Boot Process:**
1. Server should boot from USB
2. Select appropriate boot option from menu
3. Wait for live environment to load
4. Follow the troubleshooting procedures from your main document

---

## 6. Troubleshooting Common Issues

### **USB Not Detected:**
- Try different USB ports
- Use USB 2.0 port instead of USB 3.0
- Check if USB is properly formatted

### **Boot Fails:**
- Verify BIOS settings (Legacy mode, Secure Boot disabled)
- Try recreating USB with different partition scheme
- Test USB on another computer

### **Rufus Errors:**
- Run Rufus as Administrator
- Try a different USB drive
- Download ISO file again

---

## 7. Security and Best Practices

### **USB Drive Security:**
- Label the USB drive clearly
- Store in a secure location
- Create multiple copies for redundancy

### **File Management:**
- Keep original ISO file for future use
- Document the creation date and version
- Test periodically to ensure USB remains functional

---

## 8. Next Steps

After successfully creating the bootable USB:

1. **Test the USB** on a non-critical system first
2. **Follow the main troubleshooting document** (Gemini-CentOS-Boot-Solution.md)
3. **Document your recovery process** for future reference
4. **Consider creating additional recovery tools** as needed

---

## Appendix: Quick Reference

### **Recommended Download:**
- **ISO:** CentOS-7-livecd-x86_64.iso (686M)
- **Tool:** Rufus (latest version from rufus.ie)

### **Key Rufus Settings:**
- **Partition Scheme:** MBR (for maximum compatibility)
- **File System:** FAT32
- **ISO Image Mode:** Recommended

### **Critical BIOS Settings:**
- **Secure Boot:** Disabled
- **Boot Mode:** Legacy/CSM
- **Boot Order:** USB first

---

*This document was created to complement the CentOS boot troubleshooting strategy and should be used in conjunction with the main recovery procedures.*
